import { NextApiResponse } from "next";
import { Service } from "typedi";
import InterestedCreator from "./InterestedCreator";
import { Controller, NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import Information from "@src/components/interested-creators/information/Information";
@Service()
export default class UpdateApplicationController extends AuthenticatedRequestHandler implements Controller {
  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const identity = this.identity(req);
    const programCode = identity.programs[0].code;
    const nucleusId = identity.nucleusId;
    const defaultInterestedCreator = {
      originEmail: identity.email,
      nucleusId,
      defaultGamerTag: identity.username,
      dateOfBirth: identity.dateOfBirth.toString()
    } as InterestedCreator;
    const interestedCreatorFromSession = this.hasSession(req, `${programCode}.interestedCreator`)
      ? this.session(req, `${programCode}.interestedCreator`) === true
        ? defaultInterestedCreator
        : (this.session(req, `${programCode}.interestedCreator`) as InterestedCreator & typeof Information)
      : null;
    const interestedCreator = !interestedCreatorFromSession
      ? new InterestedCreator()
      : new InterestedCreator(interestedCreatorFromSession);
    const updatedCreator = { ...interestedCreator, ...req.body };

    await this.addToSession(req, `${programCode}.interestedCreator`, updatedCreator);

    this.json(res, updatedCreator);
  }
}
