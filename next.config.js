// This file sets a custom webpack configuration to use your Next.js app
// with Sentry.
// https://nextjs.org/docs/api-reference/next.config.js/introduction
// https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/
const { withSentryConfig } = require("@sentry/nextjs");
const NextFederationPlugin = require("@module-federation/nextjs-mf");

module.exports = {
  output: "standalone",
  basePath: process.env.APP_BASE_PATH,
  webpack(config) {
    config.output.publicPath = `${process.env.APP_HOST}${process.env.APP_BASE_PATH}/_next/`;
    config.output.environment = {
      ...config.output.environment,
      dynamicImport: true
    };

    config.plugins.push(
      new NextFederationPlugin({
        name: "applications",
        filename: "static/chunks/applications-mfe.js",
        exposes: {
          "./InterestedCreatorsStartPage":
            "./src/components/interested-creators/interested-creators-start-page/InterestedCreatorsStartPage.tsx",
          "./NoAccountPage": "./src/components/interested-creators/no-account-page/NoAccountPage.tsx",
          "./ApplicationCompletedPage":
            "./src/components/interested-creators/application-completed-page/ApplicationCompletedPage.tsx",
          "./ApplicationAcceptedPage":
            "./src/components/interested-creators/application-accepted-page/ApplicationAcceptedPage.tsx",
          "./AgeRestrictionPage": "./src/components/interested-creators/age-restriction-page/AgeRestrictionPage.tsx",
          "./ApplicationPendingPage":
            "./src/components/interested-creators/application-pending-page/ApplicationPendingPage.tsx",
          "./ApplicationRejectedPage":
            "./src/components/interested-creators/application-rejected-page/ApplicationRejectedPage.tsx",
          "./Information": "./src/components/interested-creators/information/Information.tsx",
          "./CreatorType": "./src/components/interested-creators/creator-type/CreatorType.tsx",
          "./FranchisesYouPlay": "./src/components/interested-creators/franchises-you-play/FranchisesYouPlay.tsx"
        }
      })
    );
    return config;
  },
  reactStrictMode: false,
  sentry: {
    hideSourceMaps: true
  },
  publicRuntimeConfig: {
    APP_ENV: process.env.APP_ENV
  },
  experimental: {
    instrumentationHook: true,
    serverComponentsExternalPackages: ["@opentelemetry/sdk-node", "next-connect"]
  }
};

module.exports = withSentryConfig(module.exports, { silent: true });
