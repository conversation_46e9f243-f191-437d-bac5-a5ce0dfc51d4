import { AccessToken, OAuthTokenProvider, TokenProvider } from "@eait-playerexp-cn/http-client";
import { RedisCache } from "@eait-playerexp-cn/server-kernel";

export default class CachedAccessTokenProvider implements TokenProvider {
  private static readonly CACHE_KEY = "accessToken";

  constructor(
    private readonly provider: OAuthTokenProvider,
    private readonly cache: RedisCache
  ) {}

  async accessToken(): Promise<AccessToken> {
    if (await this.cache.has(CachedAccessTokenProvider.CACHE_KEY)) {
      return (await this.cache.get(CachedAccessTokenProvider.CACHE_KEY)) as AccessToken;
    }

    const accessToken = await this.provider.accessToken();
    await this.cache.set(CachedAccessTokenProvider.CACHE_KEY, accessToken, accessToken.expiresIn - 5);

    return accessToken;
  }
}
