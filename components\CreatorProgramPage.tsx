/**
 * Example component showing how to use the MFE in your host application
 */

import React, { Suspense } from 'react';
import { useMFEConfigurationContext, withMFEConfiguration } from './MFEConfigurationProvider';

// Lazy load MFE components
const InterestedCreatorsStartPage = React.lazy(() => 
  import('applications/InterestedCreatorsStartPage')
);

const ApplicationPendingPage = React.lazy(() => 
  import('applications/ApplicationPendingPage')
);

const ApplicationCompletedPage = React.lazy(() => 
  import('applications/ApplicationCompletedPage')
);

const Information = React.lazy(() => 
  import('applications/Information')
);

/**
 * Loading component for MFE components
 */
const MFELoadingFallback: React.FC = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '300px',
    flexDirection: 'column',
    gap: '16px'
  }}>
    <div style={{
      width: '32px',
      height: '32px',
      border: '3px solid #f3f3f3',
      borderTop: '3px solid #3498db',
      borderRadius: '50%',
      animation: 'spin 1s linear infinite'
    }} />
    <p>Loading Creator Program...</p>
    <style jsx>{`
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `}</style>
  </div>
);

/**
 * Creator Program Start Page - Entry point for interested creators
 */
export const CreatorProgramStartPage: React.FC = withMFEConfiguration(() => {
  const { config, status } = useMFEConfigurationContext();

  return (
    <div className="creator-program-start">
      <Suspense fallback={<MFELoadingFallback />}>
        <InterestedCreatorsStartPage />
      </Suspense>
      
      {/* Debug information in development */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{
          position: 'fixed',
          bottom: '10px',
          right: '10px',
          backgroundColor: '#f8f9fa',
          padding: '10px',
          borderRadius: '4px',
          fontSize: '12px',
          maxWidth: '300px',
          border: '1px solid #dee2e6'
        }}>
          <strong>MFE Config:</strong>
          <pre>{JSON.stringify(config, null, 2)}</pre>
        </div>
      )}
    </div>
  );
});

/**
 * Creator Program Information Page
 */
export const CreatorProgramInformationPage: React.FC = withMFEConfiguration(() => {
  return (
    <div className="creator-program-information">
      <Suspense fallback={<MFELoadingFallback />}>
        <Information />
      </Suspense>
    </div>
  );
});

/**
 * Application Status Pages
 */
export const CreatorApplicationPendingPage: React.FC = withMFEConfiguration(() => {
  return (
    <div className="creator-application-pending">
      <Suspense fallback={<MFELoadingFallback />}>
        <ApplicationPendingPage />
      </Suspense>
    </div>
  );
});

export const CreatorApplicationCompletedPage: React.FC = withMFEConfiguration(() => {
  return (
    <div className="creator-application-completed">
      <Suspense fallback={<MFELoadingFallback />}>
        <ApplicationCompletedPage />
      </Suspense>
    </div>
  );
});

/**
 * Main Creator Program Page with routing logic
 */
interface CreatorProgramPageProps {
  /**
   * Current step/page to display
   * @default 'start'
   */
  currentStep?: 'start' | 'information' | 'pending' | 'completed';
  
  /**
   * Additional props to pass to the MFE component
   */
  mfeProps?: Record<string, any>;
}

export const CreatorProgramPage: React.FC<CreatorProgramPageProps> = ({
  currentStep = 'start',
  mfeProps = {}
}) => {
  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'information':
        return <CreatorProgramInformationPage {...mfeProps} />;
      case 'pending':
        return <CreatorApplicationPendingPage {...mfeProps} />;
      case 'completed':
        return <CreatorApplicationCompletedPage {...mfeProps} />;
      case 'start':
      default:
        return <CreatorProgramStartPage {...mfeProps} />;
    }
  };

  return (
    <div className="creator-program-page">
      <div className="creator-program-container">
        {renderCurrentStep()}
      </div>
    </div>
  );
};

/**
 * Hook for programmatic navigation between MFE pages
 */
export const useCreatorProgramNavigation = () => {
  const { config, isReady } = useMFEConfigurationContext();

  const navigateToApplication = () => {
    if (isReady && config.redirectUrl) {
      window.location.href = `${config.redirectUrl}/api/applications`;
    }
  };

  const navigateToLogin = () => {
    if (isReady && config.redirectUrl) {
      window.location.href = `${config.redirectUrl}/api/login`;
    }
  };

  return {
    navigateToApplication,
    navigateToLogin,
    isReady,
    config
  };
};

export default CreatorProgramPage;
