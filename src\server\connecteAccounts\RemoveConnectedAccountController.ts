import { Service } from "typedi";
import { NextApiResponse } from "next";
import { Controller, NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { HttpStatus } from "@eait-playerexp-cn/http";
import ConnectedAccountsHttpClient from "./ConnectedAccountsHttpClient";
import config from "../../../config";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";

@Service()
class RemoveConnectedAccountController extends AuthenticatedRequestHandler implements Controller {
  constructor(private readonly connectedAccounts: ConnectedAccountsHttpClient) {
    super();
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const type = config.FLAG_PER_PROGRAM_PROFILE
      ? this.identity(req).type
      : this.hasSession(req, "nucleusId")
        ? "INTERESTED_CREATOR"
        : "CREATOR";
    const accountId = req.query.id as string;
    await this.connectedAccounts.removeConnectedAccount(accountId, type);
    this.empty(res, HttpStatus.OK);
  }
}

export default RemoveConnectedAccountController;
