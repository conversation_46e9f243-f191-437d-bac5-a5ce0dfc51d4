import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { NextApiResponse } from "next";
import ViewRecentErrorsController from "@src/server/connecteAccounts/ViewRecentErrorsController";

describe("ViewRecentErrorsController", () => {
  let controller: ViewRecentErrorsController;

  beforeEach(() => jest.clearAllMocks());

  it("shows the error stored in session", async () => {
    const error = { code: "view-facebook-pages-invalid-input", message: "Please try with appropriate permissions" };
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: "/api/errors",
      session: { error }
    });
    controller = new ViewRecentErrorsController();

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(error);
  });
});
