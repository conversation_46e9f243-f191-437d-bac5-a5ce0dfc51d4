// This file configures the initialization of Sentry on the server.
// The config you add here will be used whenever the server handles a request.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";

const SENTRY_DSN = process.env.SENTRY_DSN || process.env.NEXT_PUBLIC_SENTRY_DSN;
const APP_ENV = process.env.APP_ENV || process.env.NEXT_PUBLIC_APP_ENV;

Sentry.init({
  dsn: APP_ENV === "docker" ? undefined : SENTRY_DSN,
  tracesSampleRate: APP_ENV === "prod" ? 0.8 : 1.0,
  environment: APP_ENV === "docker" ? undefined : APP_ENV,
  release: APP_ENV === "docker" ? undefined : process.env.RELEASE_VERSION
});
