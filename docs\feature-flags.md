---
currentMenu: flags
---

# Feature Flags

## FLAG_PER_PROGRAM_PROFILE

When enabled this flag will use the new middleware and request handler from the [@eait-playerexp-cn/identity](https://gitlab.ea.com/eait-playerexp-cn/creator-network-frontend/authentication/identity) package, instead of the equivalent classes from the [@eait-playerexp-cn/server-kernel](https://gitlab.ea.com/eait-playerexp-cn/creator-network-frontend/shared-kernel/server-kernel) package.
