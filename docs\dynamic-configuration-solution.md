# Dynamic Configuration Solution for Interested Creators MFE

## Overview

This document describes the implementation of dynamic configuration for the Interested Creators Micro Frontend (MFE), allowing host applications to provide `REDIRECT_URL` and `PROGRAM_CODE` at runtime instead of relying on environment variables.

## Problem Statement

Previously, the MFE relied on environment variables:
- `REDIRECT_URL` - Used for authentication redirects
- `PROGRAM_CODE` - Used for creator program identification

This approach was inflexible for a MFE that needs to be used across different applications with different configurations.

## Solution Architecture

### 1. Runtime Configuration Service

**File**: `src/services/RuntimeConfig.ts`

A singleton service that manages configuration at runtime:

```typescript
// Initialize configuration from host application
runtimeConfig.initialize({
  redirectUrl: 'https://host-app.com/creator-program',
  programCode: 'host-program-code'
});
```

**Key Features**:
- Singleton pattern for global state management
- Validation of required configuration
- Fallback to environment variables when needed
- Reset functionality for testing

### 2. MFE Initializer

**File**: `src/services/MFEInitializer.ts`

A simplified interface for host applications:

```typescript
// Easy initialization with validation
const result = initializeMFE({
  redirectUrl: 'https://host-app.com/creator-program',
  programCode: 'host-program-code'
});
```

**Key Features**:
- Simplified API for host applications
- Built-in validation with error handling
- Status checking utilities
- Configuration reset capabilities

### 3. Updated Controllers

**Files**: 
- `src/server/interestedCreator/StartApplicationController.ts`
- `src/server/interestedCreator/AddRequestToJoinProgramController.ts`

Controllers now use a priority-based configuration resolution:

1. **Runtime Configuration** (highest priority)
2. **Static Configuration** (from config files)
3. **Environment Variables** (fallback)

### 4. Module Federation Integration

**File**: `next.config.js`

The MFE exposes configuration services through Module Federation:

```javascript
exposes: {
  // ... existing components
  "./RuntimeConfig": "./src/services/RuntimeConfig.ts",
  "./MFEInitializer": "./src/services/MFEInitializer.ts"
}
```

## How Host Applications Use This

### Method 1: Simple Integration (Recommended)

```typescript
import { initializeMFE } from 'applications/MFEInitializer';

// Initialize before using MFE components
initializeMFE({
  redirectUrl: 'https://your-app.com/creator-program',
  programCode: 'your-program-code'
});
```

### Method 2: Advanced Integration

```typescript
import { runtimeConfig } from 'applications/RuntimeConfig';

// Direct configuration management
runtimeConfig.initialize({
  redirectUrl: 'https://your-app.com/creator-program',
  programCode: 'your-program-code'
});

// Validate configuration
const validation = runtimeConfig.validate();
if (!validation.isValid) {
  console.error('Missing:', validation.missingFields);
}
```

### Method 3: React Hook Integration

```typescript
const { isReady, error, retry } = useMFEConfiguration({
  redirectUrl: 'https://your-app.com/creator-program',
  programCode: 'your-program-code'
});
```

## Configuration Priority

The system uses the following priority order:

1. **Runtime Configuration** - Set by host application via `runtimeConfig.initialize()`
2. **Static Configuration** - From config files (for backward compatibility)
3. **Environment Variables** - Traditional fallback (using `*WithFallback()` methods)

## Benefits

### For Host Applications
- **Flexibility**: Configure MFE per application needs
- **Environment Independence**: No need to manage environment variables
- **Runtime Updates**: Change configuration dynamically
- **Validation**: Built-in configuration validation

### For MFE Development
- **Backward Compatibility**: Existing environment variable approach still works
- **Testability**: Easy to test with different configurations
- **Maintainability**: Clear separation of concerns

### For DevOps
- **Simplified Deployment**: Less environment variable management
- **Multi-tenant Support**: Same MFE build can serve different applications
- **Configuration Management**: Centralized configuration per host application

## Testing

Comprehensive test coverage includes:

- **Runtime Configuration Service Tests** (`__tests__/src/services/RuntimeConfig.spec.ts`)
- **Controller Tests** with runtime configuration scenarios
- **Fallback Behavior Tests** ensuring proper priority resolution

## Migration Guide

### For Existing Deployments
No changes required - environment variables continue to work as fallback.

### For New Integrations
1. Import the MFE initializer
2. Call `initializeMFE()` with your configuration
3. Use MFE components as normal

### For Testing
Use `resetMFE()` to clear configuration between tests.

## API Reference

### RuntimeConfig Service
- `initialize(config)` - Set runtime configuration
- `updateConfig(config)` - Update specific configuration values
- `getRedirectUrl()` - Get runtime redirect URL (no fallback)
- `getProgramCode()` - Get runtime program code (no fallback)
- `getRedirectUrlWithFallback()` - Get redirect URL with env fallback
- `getProgramCodeWithFallback()` - Get program code with env fallback
- `validate()` - Validate configuration completeness
- `reset()` - Reset configuration (for testing)

### MFE Initializer
- `initializeMFE(options)` - Initialize with validation
- `isMFEReady()` - Check if MFE is properly configured
- `getMFEStatus()` - Get detailed configuration status
- `resetMFE()` - Reset configuration

## Conclusion

This solution provides a flexible, maintainable approach to MFE configuration that supports both existing environment variable workflows and new dynamic configuration needs. Host applications can now easily configure the MFE at runtime while maintaining backward compatibility.
