type Country = {
  value: string;
  label: string;
  name: string;
};
type Language = {
  value: string;
  label: string;
  id: string;
};
export type ContentUrl = {
  url: string;
  followers: number;
};
type ContentAccount = {
  uri: string;
  followersCount: number;
};
export type FranchiseType = "PRIMARY" | "SECONDARY";
export type PreferredFranchise = {
  id: string;
  type: FranchiseType;
};
type CreatorType = {
  value: string;
  imageAsIcon: string;
  label: string;
  checked: boolean;
};
type PreferredLanguage = {
  code: string;
  name: string;
};
type CreatorProgram = {
  code: string;
};
type CompletedInterestedCreatorApplication = {
  defaultGamerTag: string;
  nucleusId: number;
  firstName: string;
  lastName: string;
  originEmail: string;
  dateOfBirth: string | number;
  preferredEmail: string;
  countryCode: string;
  creatorTypes: string[];
  contentLanguages: string[];
  contentAccounts: ContentAccount[];
  preferredFranchises: PreferredFranchise[];
  preferredLanguage: PreferredLanguage;
};
type CompletedInterestedCreatorApplicationWithAdditionalLinks = {
  defaultGamerTag: string;
  nucleusId: number;
  firstName: string;
  lastName: string;
  originEmail: string;
  dateOfBirth: string | number;
  preferredEmail: string;
  countryCode: string;
  creatorTypes: string[];
  contentLanguages: string[];
  additionalLinks: Array<string>;
  preferredFranchises: PreferredFranchise[];
  preferredLanguage: PreferredLanguage;
  creatorProgram: CreatorProgram;
};
class InterestedCreator {
  readonly defaultGamerTag: string;
  readonly nucleusId: number;
  readonly firstName?: string;
  readonly lastName?: string;
  readonly originEmail: string;
  readonly dateOfBirth: string | number;
  readonly preferredEmail?: string;
  readonly country?: Country;
  readonly creatorTypes?: CreatorType[] = [];
  readonly contentLanguages?: Language[] = [];
  readonly contentUrls?: ContentUrl[] = [];
  readonly preferredFranchises?: PreferredFranchise[] = [];
  readonly preferredLanguage?: PreferredLanguage;
  readonly analyticsId: string;
  readonly creatorProgram: CreatorProgram;

  constructor(interestedCreator?: InterestedCreator) {
    Object.assign(this, interestedCreator || {});
  }

  complete(): CompletedInterestedCreatorApplication {
    return {
      defaultGamerTag: this.defaultGamerTag,
      nucleusId: this.nucleusId,
      firstName: this.firstName,
      lastName: this.lastName,
      originEmail: this.originEmail,
      dateOfBirth: this.dateOfBirth,
      preferredEmail: this.preferredEmail,
      countryCode: this.country.value,
      creatorTypes: this.creatorTypes.map((creatorType) => creatorType.value),
      contentLanguages: this.contentLanguages.map((language) => language.value),
      contentAccounts: this.contentUrls.map((url) => {
        return { uri: url.url, followersCount: url.followers };
      }),
      preferredFranchises: this.preferredFranchises,
      preferredLanguage: this.preferredLanguage
    };
  }

  completeWithAdditionalLinks(): CompletedInterestedCreatorApplicationWithAdditionalLinks {
    return {
      defaultGamerTag: this.defaultGamerTag,
      nucleusId: this.nucleusId,
      firstName: this.firstName,
      lastName: this.lastName,
      originEmail: this.originEmail,
      dateOfBirth: this.dateOfBirth,
      preferredEmail: this.preferredEmail,
      countryCode: this.country.value,
      creatorTypes: this.creatorTypes.map((creatorType) => creatorType.value),
      contentLanguages: this.contentLanguages.map((language) => language.value),
      additionalLinks: this.contentUrls.filter(({ url }) => url && url !== "https://").map(({ url }) => url),
      preferredFranchises: this.preferredFranchises,
      preferredLanguage: this.preferredLanguage,
      creatorProgram: this.creatorProgram
    };
  }
}

export default InterestedCreator;
