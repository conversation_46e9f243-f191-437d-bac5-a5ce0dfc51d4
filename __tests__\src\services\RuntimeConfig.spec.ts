import { RuntimeConfigService, runtimeConfig } from '@src/services/RuntimeConfig';

describe('RuntimeConfigService', () => {
  let configService: RuntimeConfigService;

  beforeEach(() => {
    configService = new RuntimeConfigService();
  });

  afterEach(() => {
    // Reset the singleton instance
    runtimeConfig.reset();
  });

  describe('initialization', () => {
    it('should initialize with provided config', () => {
      const config = {
        redirectUrl: 'https://test.com',
        programCode: 'test-program'
      };

      configService.initialize(config);

      expect(configService.isInitialized()).toBe(true);
      expect(configService.getConfig()).toEqual(config);
    });

    it('should allow partial configuration', () => {
      // Clear environment variables for this test
      const originalProgramCode = process.env.PROGRAM_CODE;
      delete process.env.PROGRAM_CODE;

      configService.initialize({
        redirectUrl: 'https://test.com'
      });

      expect(configService.getRedirectUrl()).toBe('https://test.com');
      expect(configService.getProgramCode()).toBe(''); // Falls back to empty string

      // Restore environment variable
      if (originalProgramCode) {
        process.env.PROGRAM_CODE = originalProgramCode;
      }
    });
  });

  describe('configuration updates', () => {
    it('should update existing configuration', () => {
      configService.initialize({
        redirectUrl: 'https://initial.com',
        programCode: 'initial-program'
      });

      configService.updateConfig({
        programCode: 'updated-program'
      });

      expect(configService.getRedirectUrl()).toBe('https://initial.com');
      expect(configService.getProgramCode()).toBe('updated-program');
    });
  });

  describe('fallback behavior', () => {
    beforeAll(() => {
      // Mock environment variables
      process.env.REDIRECT_URL = 'https://env-redirect.com';
      process.env.PROGRAM_CODE = 'env-program';
    });

    afterAll(() => {
      // Clean up environment variables
      delete process.env.REDIRECT_URL;
      delete process.env.PROGRAM_CODE;
    });

    it('should fall back to environment variables when runtime config is not set', () => {
      expect(configService.getRedirectUrl()).toBe('https://env-redirect.com');
      expect(configService.getProgramCode()).toBe('env-program');
    });

    it('should prefer runtime config over environment variables', () => {
      configService.initialize({
        redirectUrl: 'https://runtime.com',
        programCode: 'runtime-program'
      });

      expect(configService.getRedirectUrl()).toBe('https://runtime.com');
      expect(configService.getProgramCode()).toBe('runtime-program');
    });
  });

  describe('validation', () => {
    it('should validate successfully when all required fields are present', () => {
      configService.initialize({
        redirectUrl: 'https://test.com',
        programCode: 'test-program'
      });

      const validation = configService.validate();
      expect(validation.isValid).toBe(true);
      expect(validation.missingFields).toEqual([]);
    });

    it('should identify missing fields', () => {
      configService.initialize({
        redirectUrl: 'https://test.com'
        // programCode is missing
      });

      const validation = configService.validate();
      expect(validation.isValid).toBe(false);
      expect(validation.missingFields).toContain('programCode');
    });

    it('should identify all missing fields', () => {
      const validation = configService.validate();
      expect(validation.isValid).toBe(false);
      expect(validation.missingFields).toEqual(['redirectUrl', 'programCode']);
    });
  });

  describe('reset functionality', () => {
    it('should reset configuration and initialization state', () => {
      configService.initialize({
        redirectUrl: 'https://test.com',
        programCode: 'test-program'
      });

      expect(configService.isInitialized()).toBe(true);

      configService.reset();

      expect(configService.isInitialized()).toBe(false);
      expect(configService.getConfig()).toEqual({});
    });
  });

  describe('singleton behavior', () => {
    it('should maintain state across imports', () => {
      runtimeConfig.initialize({
        redirectUrl: 'https://singleton-test.com',
        programCode: 'singleton-program'
      });

      // Test that the singleton maintains state
      expect(runtimeConfig.getRedirectUrl()).toBe('https://singleton-test.com');
      expect(runtimeConfig.getProgramCode()).toBe('singleton-program');
      expect(runtimeConfig.isInitialized()).toBe(true);
    });
  });
});
