#!/bin/sh

# Convert JSO<PERSON> to env, use @sh to handle values with spaces correctly
#_cn_secrets_cn-applications_$env is AWS secret name /cn/secrets/cn-applications_$env. CSI driver will mount the secret to /tmp/secret/_cn_secrets_cn-applications_$env
#And _cn_secrets_cn-applications_$env is made available to the container as MOUNTED_AWS_SECRET_NAME
jq -r 'to_entries[] | [.key,(.value|@sh)] | join("=")' /tmp/secret/$MOUNTED_AWS_SECRET_NAME > .env

applications_url=$(jq -r '.APP_HOST' /tmp/secret/$MOUNTED_AWS_SECRET_NAME)$(jq -r '.APP_BASE_PATH' /tmp/secret/$MOUNTED_AWS_SECRET_NAME)
current_applications_url=http://localhost:3003/cn-applications-mfe

echo $current_applications_url
echo $applications_url

find . \( -name '*.js' -o -name '*.json' \) ! -path "./standalone/node_modules/*" -exec sed  -i  "s,${current_applications_url},${applications_url},g" {} \;

node server.js
