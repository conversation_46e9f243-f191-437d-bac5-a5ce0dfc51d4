import { NextApiResponse } from "next";
import { Service } from "typedi";
import InterestedCreator from "./InterestedCreator";
import { Controller, NextApiRequestWithSession, RequestHandler } from "@eait-playerexp-cn/server-kernel";

@Service()
export default class LegacyUpdateApplicationController extends RequestH<PERSON>ler implements Controller {
  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const savedInterestedCreator = this.session(req, "interestedCreator") as InterestedCreator;
    const interestedCreator = !savedInterestedCreator
      ? new InterestedCreator()
      : new InterestedCreator(savedInterestedCreator);
    const updatedCreator = { ...interestedCreator, ...req.body };

    // Removing creatorNucleusId from session as nucleusId is no longer required in applicant submission.
    // We can improve session cookie storage by removing unused session keys.
    if (this.hasSession(req, "creatorNucleusId")) {
      await this.removeFromSession(req, "creatorNucleusId");
    }
    await this.addToSession(req, "interestedCreator", updatedCreator);

    this.json(res, updatedCreator);
  }
}
