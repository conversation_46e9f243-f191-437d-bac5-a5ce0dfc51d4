import "reflect-metadata";
import { NextApiResponse } from "next";
import ApiContainer from "@src/ApiContainer";
import { createRouter } from "next-connect";
import onError from "@src/middleware/OAuthErrorHandler";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  NextApiRequestWithSession,
  verifyInterestedCreatorSession
} from "@eait-playerexp-cn/server-kernel";
import session from "@src/middleware/Session";
import ViewRecentErrorsController from "@src/server/connecteAccounts/ViewRecentErrorsController";
import withCors from "@src/middleware/WithCors";
import corsPreflight from "@src/middleware/CorsPreflifight";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import config from "../../../config";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? verifySession : verifyInterestedCreatorSession)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .get(
    withCors(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
      const controller = ApiContainer.get(ViewRecentErrorsController);
      await controller.handle(req, res);
    })
  )
  .all(corsPreflight);

export default router.handler({ onError });
