import { NextApiResponse } from "next";
import ApplicationActivity from "@src/shared/logging/ApplicationActivity";
import { AxiosError } from "axios";
import { ApiProblem } from "@eait-playerexp-cn/api-problem";
import ApiContainer from "@src/ApiContainer";
import { ActivityFeed } from "@eait-playerexp-cn/activity-feed";
import { NextApiRequestWithMultipartFile, RequestFactory } from "@eait-playerexp-cn/server-kernel";

async function onOAuthError(
  error: Error | AxiosError,
  req: NextApiRequestWithMultipartFile,
  res: NextApiResponse
): Promise<void> {
  const requestFactory = new RequestFactory();
  const feed = ApiContainer.get(ActivityFeed);

  if ((error as AxiosError).response?.data) {
    const { code, message, detail } = (error as AxiosError<ApiProblem>).response.data;
    req.session.error = { code, message: message || detail }; // Error from Java API
  } else {
    feed.add(ApplicationActivity.applicationError(error, requestFactory.createRequestFrom(req)));
    req.session.error = error.message || "No message provided"; // Error message from API handler
  }

  req.session.save();

  res.end("<script>window.close();</script>");
}

export default onOAuthError;
