import { NextApiResponse } from "next";
import { Service } from "typedi";
import { Controller, NextApiRequestWithSession, RequestHandler } from "@eait-playerexp-cn/server-kernel";
import { ApiProblem } from "@eait-playerexp-cn/api-problem";
import { HttpStatus } from "@eait-playerexp-cn/http";

@Service()
class ViewFacebookPagesController extends RequestHandler implements Controller {
  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const pages = this.session(req, "fbPages");

    if (!pages) {
      this.json(res, ApiProblem.from(HttpStatus.NOT_FOUND), HttpStatus.NOT_FOUND);
      return;
    }

    this.json(res, pages);
  }
}

export default ViewFacebookPagesController;
