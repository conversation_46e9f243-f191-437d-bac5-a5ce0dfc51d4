/**
 * Runtime Configuration Service for MFE
 * Allows host applications to dynamically configure the MFE at runtime
 */

export interface MFEConfig {
  redirectUrl?: string;
  programCode?: string;
}

class RuntimeConfigService {
  private config: MFEConfig = {};
  private initialized = false;

  /**
   * Initialize configuration from host application
   * This method should be called by the host application before using the MFE
   */
  initialize(config: MFEConfig): void {
    this.config = { ...this.config, ...config };
    this.initialized = true;
    
    // Optional: Log configuration for debugging
    if (process.env.NODE_ENV === 'development') {
      console.log('[MFE RuntimeConfig] Initialized with config:', this.config);
    }
  }

  /**
   * Update specific configuration values
   * Allows partial updates after initialization
   */
  updateConfig(config: Partial<MFEConfig>): void {
    this.config = { ...this.config, ...config };
    
    if (process.env.NODE_ENV === 'development') {
      console.log('[MFE RuntimeConfig] Updated config:', this.config);
    }
  }

  /**
   * Get redirect URL - only returns runtime config value if set
   */
  getRedirectUrl(): string {
    return this.config.redirectUrl || '';
  }

  /**
   * Get program code - only returns runtime config value if set
   */
  getProgramCode(): string {
    return this.config.programCode || '';
  }

  /**
   * Get redirect URL with environment variable fallback
   */
  getRedirectUrlWithFallback(): string {
    return this.config.redirectUrl || process.env.REDIRECT_URL || '';
  }

  /**
   * Get program code with environment variable fallback
   */
  getProgramCodeWithFallback(): string {
    return this.config.programCode || process.env.PROGRAM_CODE || '';
  }

  /**
   * Get all configuration
   */
  getConfig(): MFEConfig {
    return { ...this.config };
  }

  /**
   * Check if configuration has been initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Reset configuration (useful for testing)
   */
  reset(): void {
    this.config = {};
    this.initialized = false;
  }

  /**
   * Validate that required configuration is present (including fallbacks)
   */
  validate(): { isValid: boolean; missingFields: string[] } {
    const missingFields: string[] = [];

    if (!this.getRedirectUrlWithFallback()) {
      missingFields.push('redirectUrl');
    }

    if (!this.getProgramCodeWithFallback()) {
      missingFields.push('programCode');
    }

    return {
      isValid: missingFields.length === 0,
      missingFields
    };
  }
}

// Export singleton instance
export const runtimeConfig = new RuntimeConfigService();

// Export the class for testing purposes
export { RuntimeConfigService };

// Export default for easier importing
export default runtimeConfig;
