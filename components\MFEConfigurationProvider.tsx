/**
 * MFE Configuration Provider
 * Handles initialization and management of MFE configuration for the entire application
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useMFEConfiguration } from '../hooks/useMFEConfiguration';

interface MFEConfig {
  redirectUrl: string;
  programCode: string;
}

interface MFEConfigurationContextType {
  isReady: boolean;
  isLoading: boolean;
  error: string | null;
  config: MFEConfig;
  retry: () => void;
  status: any;
}

const MFEConfigurationContext = createContext<MFEConfigurationContextType | null>(null);

interface MFEConfigurationProviderProps {
  children: React.ReactNode;
  config: MFEConfig;
  /**
   * Whether to show loading state while MFE is being configured
   * @default true
   */
  showLoadingState?: boolean;
  /**
   * Whether to show error state when MFE configuration fails
   * @default true
   */
  showErrorState?: boolean;
  /**
   * Custom loading component
   */
  loadingComponent?: React.ReactNode;
  /**
   * Custom error component
   */
  errorComponent?: (error: string, retry: () => void) => React.ReactNode;
}

/**
 * Provider component that initializes MFE configuration and provides it to child components
 */
export const MFEConfigurationProvider: React.FC<MFEConfigurationProviderProps> = ({
  children,
  config,
  showLoadingState = true,
  showErrorState = true,
  loadingComponent,
  errorComponent
}) => {
  const mfeState = useMFEConfiguration({
    redirectUrl: config.redirectUrl,
    programCode: config.programCode,
    validate: true,
    throwOnValidationError: false,
    autoRetry: true,
    maxRetries: 3,
    onValidationError: (missingFields) => {
      console.warn('🚨 MFE Configuration validation failed:', missingFields);
    }
  });

  const { isReady, isLoading, error, retry, status } = mfeState;

  // Log configuration status for debugging
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 MFE Configuration Status:', {
        isReady,
        isLoading,
        error,
        config,
        status
      });
    }
  }, [isReady, isLoading, error, config, status]);

  const contextValue: MFEConfigurationContextType = {
    isReady,
    isLoading,
    error,
    config,
    retry,
    status
  };

  // Show loading state
  if (showLoadingState && isLoading) {
    if (loadingComponent) {
      return <>{loadingComponent}</>;
    }
    
    return (
      <div className="mfe-loading-container" style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '200px',
        flexDirection: 'column',
        gap: '16px'
      }}>
        <div className="mfe-loading-spinner" style={{
          width: '40px',
          height: '40px',
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #3498db',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }} />
        <p>Configuring Creator Program...</p>
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  // Show error state
  if (showErrorState && error && !isLoading) {
    if (errorComponent) {
      return <>{errorComponent(error, retry)}</>;
    }
    
    return (
      <div className="mfe-error-container" style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '200px',
        flexDirection: 'column',
        gap: '16px',
        padding: '20px',
        textAlign: 'center'
      }}>
        <div style={{ color: '#e74c3c', fontSize: '48px' }}>⚠️</div>
        <h3 style={{ color: '#e74c3c', margin: 0 }}>Creator Program Configuration Error</h3>
        <p style={{ color: '#666', margin: 0 }}>{error}</p>
        <button 
          onClick={retry}
          style={{
            padding: '10px 20px',
            backgroundColor: '#3498db',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Retry Configuration
        </button>
        {process.env.NODE_ENV === 'development' && (
          <details style={{ marginTop: '16px', textAlign: 'left' }}>
            <summary style={{ cursor: 'pointer', color: '#666' }}>Debug Information</summary>
            <pre style={{ 
              backgroundColor: '#f8f9fa', 
              padding: '10px', 
              borderRadius: '4px',
              fontSize: '12px',
              overflow: 'auto'
            }}>
              {JSON.stringify({ config, status }, null, 2)}
            </pre>
          </details>
        )}
      </div>
    );
  }

  return (
    <MFEConfigurationContext.Provider value={contextValue}>
      {children}
    </MFEConfigurationContext.Provider>
  );
};

/**
 * Hook to access MFE configuration context
 */
export const useMFEConfigurationContext = (): MFEConfigurationContextType => {
  const context = useContext(MFEConfigurationContext);
  if (!context) {
    throw new Error('useMFEConfigurationContext must be used within a MFEConfigurationProvider');
  }
  return context;
};

/**
 * HOC to wrap components that need MFE configuration
 */
export const withMFEConfiguration = <P extends object>(
  Component: React.ComponentType<P>
) => {
  const WrappedComponent = (props: P) => {
    const mfeContext = useMFEConfigurationContext();
    
    if (!mfeContext.isReady) {
      return null; // or a loading state
    }
    
    return <Component {...props} />;
  };
  
  WrappedComponent.displayName = `withMFEConfiguration(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

export default MFEConfigurationProvider;
