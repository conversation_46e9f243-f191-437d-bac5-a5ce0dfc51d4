import InterestedCreator from "@src/server/interestedCreator/InterestedCreator";
import {
  Random,
  aSecondaryFranchise,
  aPrimaryFranchise,
  anInterestedCreator
} from "@eait-playerexp-cn/interested-creators-ui";
import { aCountry, aCreatorType, aLanguage } from "@eait-playerexp-cn/metadata-test-fixtures";
import { Country, CountryResponse } from "@eait-playerexp-cn/metadata-types";

describe("InterestedCreator", () => {
  const creatorTypes = [aCreatorType(), aCreatorType()];
  const countryCode = "US";
  const creatorCountry = Country.fromApi(aCountry({ code: countryCode }) as unknown as CountryResponse);
  const updatedPreferredEmail = Random.email();
  const preferredFranchises = [aPrimaryFranchise(), aSecondaryFranchise()];
  const interestedCreator = anInterestedCreator({
    country: creatorCountry,
    contentLanguages: [aLanguage({ value: "en" })],
    preferredFranchises: preferredFranchises,
    preferredLanguage: aCountry({ code: "en_US", name: "English" }),
    creatorTypes,
    preferredEmail: updatedPreferredEmail
  });
  const creatorProgram = {
    code: "affiliate"
  };
  const url = Random.url();
  const expectedCompletedApplication = {
    defaultGamerTag: interestedCreator.defaultGamerTag,
    nucleusId: interestedCreator.nucleusId,
    firstName: interestedCreator.firstName,
    lastName: interestedCreator.lastName,
    originEmail: interestedCreator.originEmail,
    dateOfBirth: interestedCreator.dateOfBirth,
    preferredEmail: interestedCreator.preferredEmail,
    countryCode,
    creatorTypes: [
      (interestedCreator.creatorTypes[0] as unknown as { value: string }).value,
      (interestedCreator.creatorTypes[1] as unknown as { value: string })?.value
    ],
    contentLanguages: [interestedCreator.contentLanguages[0].value],
    preferredFranchises: interestedCreator.preferredFranchises,
    preferredLanguage: interestedCreator.preferredLanguage
  };

  it("formats the content urls and its followers", async () => {
    const interestedCreatorWithContentUrls = new InterestedCreator({
      ...interestedCreator,
      contentUrls: [{ url: url, followers: Random.number(1, 100) }]
    } as unknown as InterestedCreator);
    const expectedCompletedApplicationWithContentUrls = {
      ...expectedCompletedApplication,
      contentAccounts: [
        {
          uri: interestedCreatorWithContentUrls.contentUrls[0].url,
          followersCount: interestedCreatorWithContentUrls.contentUrls[0].followers
        }
      ]
    };

    const completedApplication = interestedCreatorWithContentUrls.complete();

    expect(completedApplication).toEqual(expectedCompletedApplicationWithContentUrls);
  });

  it("formats the content urls as additional links", async () => {
    const interestedCreatorWithAdditionalLinks = new InterestedCreator({
      ...interestedCreator,
      contentUrls: [{ url: url, followers: 0 }],
      creatorProgram
    } as unknown as InterestedCreator);
    const expectedCompletedApplicationWithAdditionalLinks = {
      ...expectedCompletedApplication,
      additionalLinks: [url],
      creatorProgram
    };

    const completedApplication = interestedCreatorWithAdditionalLinks.completeWithAdditionalLinks();

    expect(completedApplication).toEqual(expectedCompletedApplicationWithAdditionalLinks);
  });

  it("it removes empty or invalid URLs", async () => {
    const interestedCreatorWithAdditionalLinks = new InterestedCreator({
      ...interestedCreator,
      contentUrls: [{ url: "https://", followers: 0 }],
      creatorProgram
    } as unknown as InterestedCreator);
    const expectedCompletedApplicationWithAdditionalLinks = {
      ...expectedCompletedApplication,
      creatorProgram,
      additionalLinks: []
    };

    const completedApplication = interestedCreatorWithAdditionalLinks.completeWithAdditionalLinks();

    expect(completedApplication).toEqual(expectedCompletedApplicationWithAdditionalLinks);
  });
});
