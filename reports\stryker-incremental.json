{"files": {"src/pages/index.tsx": {"language": "typescript", "mutants": [{"id": "0", "mutatorName": "ArrowFunction", "replacement": "() => undefined", "statusReason": "Error: Unable to find an element with the text: /Applications Module Federation/i. This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible.\n\nIgnored nodes: comments, script, style\n\u001b[36m<body>\u001b[39m\n  \u001b[36m<div />\u001b[39m\n\u001b[36m</body>\u001b[39m\n    at waitForWrapper (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\@testing-library\\dom\\dist\\wait-for.js:163:27)\n    at C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\@testing-library\\dom\\dist\\query-helpers.js:86:33\n    at Object.findByText (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2151364\\__tests__\\src\\pages\\index.spec.tsx:9:25)\n    at Promise.then.completed (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)", "status": "Killed", "testsCompleted": 1, "static": true, "killedBy": ["33"], "coveredBy": [], "location": {"end": {"column": 2, "line": 15}, "start": {"column": 24, "line": 3}}}], "source": "import Head from \"next/head\";\r\n\r\nconst Home: React.FC = () => (\r\n  <>\r\n    <Head>\r\n      <title>Template for Electronic Arts Module Federation</title>\r\n      <meta name=\"description\" content=\"Template for Electronic Arts Module Federation\" />\r\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\r\n      <link rel=\"icon\" href=\"/favicon.ico\" />\r\n    </Head>\r\n    <main>\r\n      <div className=\"text-white\">Applications Module Federation</div>\r\n    </main>\r\n  </>\r\n);\r\n\r\nexport default Home;\r\n"}, "src/server/channels/facebook/ClearFacebookPagesController.ts": {"language": "typescript", "mutants": [{"id": "1", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 1\nReceived number of calls: 0\n    at Object.toHaveBeenCalledTimes (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\channels\\facebook\\ClearFacebookPagesController.spec.ts:26:30)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["30"], "coveredBy": ["30"], "location": {"end": {"column": 4, "line": 11}, "start": {"column": 85, "line": 8}}}, {"id": "2", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "Survived", "testsCompleted": 1, "static": false, "killedBy": [], "coveredBy": ["30"], "location": {"end": {"column": 48, "line": 9}, "start": {"column": 39, "line": 9}}}], "source": "import { NextApiResponse } from \"next\";\r\nimport { Service } from \"typedi\";\r\nimport { Controller, NextApiRequestWithSession, RequestHandler } from \"@eait-playerexp-cn/server-kernel\";\r\nimport { HttpStatus } from \"@eait-playerexp-cn/http\";\r\n\r\n@Service()\r\nclass ClearFacebookPagesController extends RequestHandler implements Controller {\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    await this.removeFromSession(req, \"fbPages\");\r\n    this.empty(res, HttpStatus.OK);\r\n  }\r\n}\r\n\r\nexport default ClearFacebookPagesController;\r\n"}, "src/server/channels/facebook/ConnectFacebookPageController.ts": {"language": "typescript", "mutants": [{"id": "3", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\nExpected: \"Facebook\"\nReceived: undefined\n    at Object.toEqual (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox6700620\\__tests__\\src\\server\\channels\\facebook\\ConnectFacebookPageController.spec.ts:43:37)", "status": "Killed", "testsCompleted": 3, "static": false, "killedBy": ["0"], "coveredBy": ["0", "1", "2", "3", "4"], "location": {"end": {"column": 4, "line": 42}, "start": {"column": 85, "line": 16}}}, {"id": "8", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "TypeError: Cannot read properties of null (reading 'id')\n    at ConnectFacebookPageController.id [as handle] (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox6700620\\src\\server\\channels\\facebook\\ConnectFacebookPageController.ts:85:113)\n    at Object.handle (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox6700620\\__tests__\\src\\server\\channels\\facebook\\ConnectFacebookPageController.spec.ts:39:22)\n    at Promise.then.completed (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)", "status": "Killed", "testsCompleted": 3, "static": false, "killedBy": ["0"], "coveredBy": ["0", "1", "2"], "location": {"end": {"column": 41, "line": 19}, "start": {"column": 30, "line": 19}}}, {"id": "9", "mutatorName": "ConditionalExpression", "replacement": "true", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n- Object {\n-   \"creatorId\": \"15d7210f-0882-4b33-87e9-dcd5fea75e3d\",\n-   \"nucleusId\": undefined,\n+ FacebookPageCredentials {\n+   \"creatorId\": null,\n+   \"nucleusId\": null,\n    \"pageAccessToken\": \"qIvyuUNR\",\n    \"pageId\": \"a057717c-01f2-49de-9422-289b06649809\",\n  },\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox6700620\\__tests__\\src\\server\\channels\\facebook\\ConnectFacebookPageController.spec.ts:70:51)", "status": "Killed", "testsCompleted": 3, "static": false, "killedBy": ["1"], "coveredBy": ["0", "1", "2", "3", "4"], "location": {"end": {"column": 28, "line": 24}, "start": {"column": 9, "line": 24}}}, {"id": "10", "mutatorName": "ConditionalExpression", "replacement": "false", "statusReason": "TypeError: Cannot read properties of null (reading 'id')\n    at ConnectFacebookPageController.id [as handle] (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox6700620\\src\\server\\channels\\facebook\\ConnectFacebookPageController.ts:85:113)\n    at Object.handle (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox6700620\\__tests__\\src\\server\\channels\\facebook\\ConnectFacebookPageController.spec.ts:39:22)\n    at Promise.then.completed (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)", "status": "Killed", "testsCompleted": 3, "static": false, "killedBy": ["0"], "coveredBy": ["0", "1", "2", "3", "4"], "location": {"end": {"column": 28, "line": 24}, "start": {"column": 9, "line": 24}}}, {"id": "11", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: {\"creatorId\": null, \"nucleusId\": \"*********\", \"pageAccessToken\": \"4RV1uZNx\", \"pageId\": \"a057717c-01f2-49de-9422-289b06649809\"}\nReceived: undefined\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox6700620\\__tests__\\src\\server\\channels\\facebook\\ConnectFacebookPageController.spec.ts:46:51)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["0"], "coveredBy": ["0", "2", "3"], "location": {"end": {"column": 6, "line": 30}, "start": {"column": 30, "line": 24}}}, {"id": "12", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: {\"creatorId\": \"15d7210f-0882-4b33-87e9-dcd5fea75e3d\", \"nucleusId\": undefined, \"pageAccessToken\": \"BFyRVXwM\", \"pageId\": \"a057717c-01f2-49de-9422-289b06649809\"}\nReceived: undefined\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox6700620\\__tests__\\src\\server\\channels\\facebook\\ConnectFacebookPageController.spec.ts:70:51)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["1"], "coveredBy": ["1", "4"], "location": {"end": {"column": 6, "line": 33}, "start": {"column": 12, "line": 30}}}, {"id": "14", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\nExpected: \"Facebook\"\nReceived: undefined\n    at Object.toEqual (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox6700620\\__tests__\\src\\server\\channels\\facebook\\ConnectFacebookPageController.spec.ts:43:37)", "status": "Killed", "testsCompleted": 3, "static": false, "killedBy": ["0"], "coveredBy": ["0", "1", "2", "3", "4"], "location": {"end": {"column": 47, "line": 39}, "start": {"column": 34, "line": 39}}}, {"id": "15", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\nExpected: \"Facebook\"\nReceived: \"\"\n    at Object.toEqual (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox6700620\\__tests__\\src\\server\\channels\\facebook\\ConnectFacebookPageController.spec.ts:43:37)", "status": "Killed", "testsCompleted": 3, "static": false, "killedBy": ["0"], "coveredBy": ["0", "1", "2", "3", "4"], "location": {"end": {"column": 59, "line": 39}, "start": {"column": 49, "line": 39}}}, {"id": "7", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "src/server/channels/facebook/ConnectFacebookPageController.ts(18,9): error TS2367: This comparison appears to be unintentional because the types 'IdentityType' and '\"\"' have no overlap.\r\n", "status": "CompileError", "static": false, "coveredBy": ["3", "4"], "location": {"end": {"column": 57, "line": 18}, "start": {"column": 37, "line": 18}}}, {"id": "4", "mutatorName": "ConditionalExpression", "replacement": "true", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n- Object {\n-   \"creatorId\": \"creator-id-12345\",\n+ FacebookPageCredentials {\n+   \"creatorId\": null,\n    \"nucleusId\": undefined,\n    \"pageAccessToken\": \"RFJflVrP\",\n    \"pageId\": \"a057717c-01f2-49de-9422-289b06649809\",\n  },\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox680216\\__tests__\\src\\server\\channels\\facebook\\ConnectFacebookPageController.spec.ts:180:51)", "status": "Killed", "static": false, "testsCompleted": 2, "killedBy": ["4"], "coveredBy": ["3", "4"], "location": {"end": {"column": 57, "line": 18}, "start": {"column": 9, "line": 18}}}, {"id": "6", "mutatorName": "EqualityOperator", "replacement": "this.identity(req).type !== \"INTERESTED_CREATOR\"", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n- Object {\n-   \"creatorId\": null,\n-   \"nucleusId\": *********,\n+ FacebookPageCredentials {\n+   \"creatorId\": undefined,\n+   \"nucleusId\": undefined,\n    \"pageAccessToken\": \"9mATLPi7\",\n    \"pageId\": \"a057717c-01f2-49de-9422-289b06649809\",\n  },\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox680216\\__tests__\\src\\server\\channels\\facebook\\ConnectFacebookPageController.spec.ts:142:51)", "status": "Killed", "static": false, "testsCompleted": 2, "killedBy": ["3"], "coveredBy": ["3", "4"], "location": {"end": {"column": 57, "line": 18}, "start": {"column": 9, "line": 18}}}, {"id": "5", "mutatorName": "ConditionalExpression", "replacement": "false", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n- Object {\n-   \"creatorId\": null,\n-   \"nucleusId\": *********,\n+ FacebookPageCredentials {\n+   \"creatorId\": undefined,\n+   \"nucleusId\": undefined,\n    \"pageAccessToken\": \"3UBRePhW\",\n    \"pageId\": \"a057717c-01f2-49de-9422-289b06649809\",\n  },\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox680216\\__tests__\\src\\server\\channels\\facebook\\ConnectFacebookPageController.spec.ts:142:51)", "status": "Killed", "static": false, "testsCompleted": 2, "killedBy": ["3"], "coveredBy": ["3", "4"], "location": {"end": {"column": 57, "line": 18}, "start": {"column": 9, "line": 18}}}, {"id": "13", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "Survived", "static": false, "testsCompleted": 5, "coveredBy": ["0", "1", "2", "3", "4"], "location": {"end": {"column": 48, "line": 35}, "start": {"column": 39, "line": 35}}}], "source": "import { NextApiResponse } from \"next\";\r\nimport { AuthenticatedRequestHandler } from \"@eait-playerexp-cn/identity\";\r\nimport FacebookPageCredentials from \"./FacebookPageCredentials\";\r\nimport { Service } from \"typedi\";\r\nimport { Controller, NextApiRequestWithSession } from \"@eait-playerexp-cn/server-kernel\";\r\nimport { HttpStatus } from \"@eait-playerexp-cn/http\";\r\nimport ConnectedAccountsHttpClient from \"../../connecteAccounts/ConnectedAccountsHttpClient\";\r\nimport config from \"../../../../config\";\r\n\r\n@Service()\r\nclass ConnectFacebookPageController extends AuthenticatedRequestHandler implements Controller {\r\n  constructor(private readonly connectedAccounts: ConnectedAccountsHttpClient) {\r\n    super();\r\n  }\r\n\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    const isInterestedCreator = config.FLAG_PER_PROGRAM_PROFILE\r\n      ? this.identity(req).type === \"INTERESTED_CREATOR\"\r\n      : this.hasSession(req, \"nucleusId\");\r\n\r\n    const { pageId, pageAccessToken } = req.body;\r\n    let credentials;\r\n\r\n    if (isInterestedCreator) {\r\n      credentials = FacebookPageCredentials.forInterestedCreator(\r\n        pageId as string,\r\n        pageAccessToken as string,\r\n        config.FLAG_PER_PROGRAM_PROFILE ? this.identity(req).nucleusId : (this.session(req, \"nucleusId\") as number)\r\n      );\r\n    } else {\r\n      const creator = config.FLAG_PER_PROGRAM_PROFILE ? this.identity(req) : this.authenticatedUser(req);\r\n      credentials = FacebookPageCredentials.forCreator(pageId as string, pageAccessToken as string, creator.id);\r\n    }\r\n\r\n    await this.removeFromSession(req, \"fbPages\");\r\n\r\n    await this.connectedAccounts.connectFacebookPage(credentials);\r\n\r\n    await this.addToSession(req, \"accountType\", \"Facebook\");\r\n\r\n    this.empty(res, HttpStatus.OK);\r\n  }\r\n}\r\n\r\nexport default ConnectFacebookPageController;\r\n"}, "src/server/channels/facebook/ViewFacebookPagesController.ts": {"language": "typescript", "mutants": [{"id": "18", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at Object.parse (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\channels\\facebook\\ViewFacebookPagesController.spec.ts:25:17)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["26"], "coveredBy": ["26", "27"], "location": {"end": {"column": 4, "line": 18}, "start": {"column": 85, "line": 9}}}, {"id": "19", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(received).toBe(expected) // Object.is equality\n\nExpected: 200\nReceived: 404\n    at Object.toBe (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\channels\\facebook\\ViewFacebookPagesController.spec.ts:24:34)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["26"], "coveredBy": ["26", "27"], "location": {"end": {"column": 46, "line": 10}, "start": {"column": 37, "line": 10}}}, {"id": "20", "mutatorName": "<PERSON>olean<PERSON>iter<PERSON>", "replacement": "pages", "statusReason": "Error: expect(received).toBe(expected) // Object.is equality\n\nExpected: 200\nReceived: 404\n    at Object.toBe (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\channels\\facebook\\ViewFacebookPagesController.spec.ts:24:34)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["26"], "coveredBy": ["26", "27"], "location": {"end": {"column": 15, "line": 12}, "start": {"column": 9, "line": 12}}}, {"id": "21", "mutatorName": "ConditionalExpression", "replacement": "true", "statusReason": "Error: expect(received).toBe(expected) // Object.is equality\n\nExpected: 200\nReceived: 404\n    at Object.toBe (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\channels\\facebook\\ViewFacebookPagesController.spec.ts:24:34)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["26"], "coveredBy": ["26", "27"], "location": {"end": {"column": 15, "line": 12}, "start": {"column": 9, "line": 12}}}, {"id": "22", "mutatorName": "ConditionalExpression", "replacement": "false", "statusReason": "Error: expect(received).toBe(expected) // Object.is equality\n\nExpected: 404\nReceived: 200\n    at Object.toBe (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\channels\\facebook\\ViewFacebookPagesController.spec.ts:38:34)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["27"], "coveredBy": ["26", "27"], "location": {"end": {"column": 15, "line": 12}, "start": {"column": 9, "line": 12}}}, {"id": "23", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(received).toBe(expected) // Object.is equality\n\nExpected: 404\nReceived: 200\n    at Object.toBe (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\channels\\facebook\\ViewFacebookPagesController.spec.ts:38:34)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["27"], "coveredBy": ["27"], "location": {"end": {"column": 6, "line": 15}, "start": {"column": 17, "line": 12}}}], "source": "import { NextApiResponse } from \"next\";\r\nimport { Service } from \"typedi\";\r\nimport { Controller, NextApiRequestWithSession, RequestHandler } from \"@eait-playerexp-cn/server-kernel\";\r\nimport { ApiProblem } from \"@eait-playerexp-cn/api-problem\";\r\nimport { HttpStatus } from \"@eait-playerexp-cn/http\";\r\n\r\n@Service()\r\nclass ViewFacebookPagesController extends RequestHandler implements Controller {\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    const pages = this.session(req, \"fbPages\");\r\n\r\n    if (!pages) {\r\n      this.json(res, ApiProblem.from(HttpStatus.NOT_FOUND), HttpStatus.NOT_FOUND);\r\n      return;\r\n    }\r\n\r\n    this.json(res, pages);\r\n  }\r\n}\r\n\r\nexport default ViewFacebookPagesController;\r\n"}, "src/server/connecteAccounts/ClearAccountTypeController.ts": {"language": "typescript", "mutants": [{"id": "24", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 1\nReceived number of calls: 0\n    at Object.toHaveBeenCalledTimes (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\connecteAccounts\\ClearAccountTypeController.spec.ts:26:30)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["32"], "coveredBy": ["32"], "location": {"end": {"column": 4, "line": 11}, "start": {"column": 85, "line": 8}}}, {"id": "25", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "Survived", "testsCompleted": 1, "static": false, "killedBy": [], "coveredBy": ["32"], "location": {"end": {"column": 52, "line": 9}, "start": {"column": 39, "line": 9}}}], "source": "import { NextApiResponse } from \"next\";\r\nimport { Service } from \"typedi\";\r\nimport { <PERSON>, NextApiRequestWithSession, RequestHandler } from \"@eait-playerexp-cn/server-kernel\";\r\nimport { HttpStatus } from \"@eait-playerexp-cn/http\";\r\n\r\n@Service()\r\nclass ClearAccountTypeController extends Request<PERSON>andler implements Controller {\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    await this.removeFromSession(req, \"accountType\");\r\n    this.empty(res, HttpStatus.OK);\r\n  }\r\n}\r\n\r\nexport default ClearAccountTypeController;\r\n"}, "src/server/connecteAccounts/ClearSessionsKeyController.ts": {"language": "typescript", "mutants": [{"id": "26", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/connecteAccounts/ClearSessionsKeyController.ts(8,3): error TS2377: Constructors for derived classes must contain a 'super' call.\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["31"], "location": {"end": {"column": 4, "line": 10}, "start": {"column": 17, "line": 8}}}, {"id": "27", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 1\nReceived number of calls: 0\n    at Object.toHaveBeenCalledTimes (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\connecteAccounts\\ClearSessionsKeyController.spec.ts:27:30)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["31"], "coveredBy": ["31"], "location": {"end": {"column": 4, "line": 16}, "start": {"column": 85, "line": 12}}}], "source": "import { NextApiResponse } from \"next\";\r\nimport { Service } from \"typedi\";\r\nimport { <PERSON>, NextApiRequestWithSession, RequestHandler } from \"@eait-playerexp-cn/server-kernel\";\r\nimport { HttpStatus } from \"@eait-playerexp-cn/http\";\r\n\r\n@Service()\r\nclass ClearSessionsKeyController extends RequestHandler implements Controller {\r\n  constructor() {\r\n    super();\r\n  }\r\n\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    const sessionKey = req.query.key as string;\r\n    await this.removeFromSession(req, sessionKey);\r\n    this.empty(res, HttpStatus.OK);\r\n  }\r\n}\r\n\r\nexport default ClearSessionsKeyController;\r\n"}, "src/server/connecteAccounts/ConnectedAccountsHttpClient.ts": {"language": "typescript", "mutants": [{"id": "28", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/connecteAccounts/ConnectedAccountsHttpClient.ts(19,53): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["5"], "location": {"end": {"column": 4, "line": 22}, "start": {"column": 86, "line": 19}}}, {"id": "29", "mutatorName": "StringLiteral", "replacement": "``", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"/v1/connected-accounts/123456\"\nReceived: \"\"\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox6034802\\__tests__\\src\\server\\connecteAccounts\\ConnectedAccountsHttpClient.spec.ts:29:24)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["5"], "coveredBy": ["5"], "location": {"end": {"column": 81, "line": 20}, "start": {"column": 44, "line": 20}}}, {"id": "30", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/connecteAccounts/ConnectedAccountsHttpClient.ts(27,73): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["6", "7"], "location": {"end": {"column": 4, "line": 30}, "start": {"column": 106, "line": 27}}}, {"id": "31", "mutatorName": "StringLiteral", "replacement": "``", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"/v2/connected-accounts/123456\"\nReceived: \"\"\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox927583\\__tests__\\src\\server\\connecteAccounts\\ConnectedAccountsHttpClient.spec.ts:53:24)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["6"], "coveredBy": ["6", "7"], "location": {"end": {"column": 81, "line": 28}, "start": {"column": 44, "line": 28}}}, {"id": "32", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 1\nReceived number of calls: 0\n    at Object.toHaveBeenCalledTimes (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox927583\\__tests__\\src\\server\\connecteAccounts\\ConnectedAccountsHttpClient.spec.ts:95:25)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["8"], "coveredBy": ["8"], "location": {"end": {"column": 4, "line": 38}, "start": {"column": 82, "line": 35}}}, {"id": "33", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n- \"/v1/facebook-accounts\",\n+ \"\",\n  {\"body\": {\"accessToken\": \"WS6tuXG1\", \"creatorId\": null, \"nucleusId\": 123456, \"pageAccessToken\": \"R9Fq7jn2\", \"pageId\": \"a057717c-01f2-49de-9422-289b06649809\"}},\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox927583\\__tests__\\src\\server\\connecteAccounts\\ConnectedAccountsHttpClient.spec.ts:96:25)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["8"], "coveredBy": ["8"], "location": {"end": {"column": 51, "line": 36}, "start": {"column": 28, "line": 36}}}, {"id": "34", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n  \"/v1/facebook-accounts\",\n- Object {\n-   \"body\": Object {\n-     \"accessToken\": \"g9wLrNjC\",\n-     \"creatorId\": null,\n-     \"nucleusId\": 123456,\n-     \"pageAccessToken\": \"20fpY0Is\",\n-     \"pageId\": \"a057717c-01f2-49de-9422-289b06649809\",\n-   },\n- }\n+ Object {},\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox927583\\__tests__\\src\\server\\connecteAccounts\\ConnectedAccountsHttpClient.spec.ts:96:25)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["8"], "coveredBy": ["8"], "location": {"end": {"column": 74, "line": 36}, "start": {"column": 53, "line": 36}}}, {"id": "35", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 1\nReceived number of calls: 0\n    at Object.toHaveBeenCalledTimes (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\connecteAccounts\\ConnectedAccountsHttpClient.spec.ts:58:27)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["9"], "coveredBy": ["9"], "location": {"end": {"column": 4, "line": 46}, "start": {"column": 73, "line": 43}}}, {"id": "36", "mutatorName": "StringLiteral", "replacement": "``", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n- \"/v1/connected-accounts/a1LDF00000K4z0i2AB\",\n+ \"\",\n  {\"query\": {\"type\": \"INTERESTED_CREATOR\"}},\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\connecteAccounts\\ConnectedAccountsHttpClient.spec.ts:59:27)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["9"], "coveredBy": ["9"], "location": {"end": {"column": 60, "line": 44}, "start": {"column": 30, "line": 44}}}, {"id": "37", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n  \"/v1/connected-accounts/a1LDF00000K4z0i2AB\",\n- Object {\n-   \"query\": Object {\n-     \"type\": \"INTERESTED_CREATOR\",\n-   },\n- }\n+ Object {},\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\connecteAccounts\\ConnectedAccountsHttpClient.spec.ts:59:27)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["9"], "coveredBy": ["9"], "location": {"end": {"column": 81, "line": 44}, "start": {"column": 62, "line": 44}}}, {"id": "38", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n  \"/v1/connected-accounts/a1LDF00000K4z0i2AB\",\n  Object {\n-   \"query\": Object {\n-     \"type\": \"INTERESTED_CREATOR\",\n-   },\n+   \"query\": Object {},\n  },\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\connecteAccounts\\ConnectedAccountsHttpClient.spec.ts:59:27)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["9"], "coveredBy": ["9"], "location": {"end": {"column": 79, "line": 44}, "start": {"column": 71, "line": 44}}}], "source": "import { Inject, Service } from \"typedi\";\r\nimport FacebookPageCredentials from \"../channels/facebook/FacebookPageCredentials\";\r\nimport { TraceableHttpClient } from \"@eait-playerexp-cn/http-client\";\r\nimport { ConnectedAccount } from \"@eait-playerexp-cn/interested-creators-ui\";\r\n\r\nexport type FacebookPage = {\r\n  id: string;\r\n  accessToken: string;\r\n  name: string;\r\n};\r\n\r\n@Service()\r\nclass ConnectedAccountsHttpClient {\r\n  constructor(@Inject(\"contentSubmissionClient\") private client: TraceableHttpClient) {}\r\n\r\n  /**\r\n   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Social-Channels/operation/viewInterestedCreatorConnectedAccounts}\r\n   */\r\n  async getAllConnectedAccounts(nucleusId: number): Promise<Array<ConnectedAccount>> {\r\n    const response = await this.client.get(`/v1/connected-accounts/${nucleusId}`);\r\n    return Promise.resolve(response.data);\r\n  }\r\n\r\n  /**\r\n   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-submission/content-submission-api/docs/api.html#tag/Social-Channels/operation/viewInterestedCreatorConnectedAccountsV2}\r\n   */\r\n  async getAllConnectedAccountsWithExpirationStatus(nucleusId: number): Promise<Array<ConnectedAccount>> {\r\n    const response = await this.client.get(`/v2/connected-accounts/${nucleusId}`);\r\n    return Promise.resolve(response.data);\r\n  }\r\n\r\n  /**\r\n   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Social-Channels/operation/saveFacebookAccount}\r\n   */\r\n  async connectFacebookPage(credentials: FacebookPageCredentials): Promise<void> {\r\n    await this.client.post(\"/v1/facebook-accounts\", { body: credentials });\r\n    return Promise.resolve();\r\n  }\r\n\r\n  /**\r\n   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Social-Channels/operation/disconnectAccount}\r\n   */\r\n  async removeConnectedAccount(id: string, type: string): Promise<void> {\r\n    await this.client.delete(`/v1/connected-accounts/${id}`, { query: { type } });\r\n    return Promise.resolve();\r\n  }\r\n}\r\n\r\nexport default ConnectedAccountsHttpClient;\r\n"}, "src/server/connecteAccounts/RemoveConnectedAccountController.ts": {"language": "typescript", "mutants": [{"id": "39", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 1\nReceived number of calls: 0\n    at Object.toHaveBeenCalledTimes (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox6034802\\__tests__\\src\\server\\connecteAccounts\\RemoveConnectedAccountController.spec.ts:29:45)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["22"], "coveredBy": ["22", "23"], "location": {"end": {"column": 4, "line": 24}, "start": {"column": 85, "line": 15}}}, {"id": "40", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"a1LDF00000K4z0i2AB\", \"INTERESTED_CREATOR\"\nReceived: \"a1LDF00000K4z0i2AB\", \"CREATOR\"\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox6034802\\__tests__\\src\\server\\connecteAccounts\\RemoveConnectedAccountController.spec.ts:30:45)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["22"], "coveredBy": ["22", "23"], "location": {"end": {"column": 41, "line": 18}, "start": {"column": 30, "line": 18}}}, {"id": "41", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"a1LDF00000K4z0i2AB\", \"INTERESTED_CREATOR\"\nReceived: \"a1LDF00000K4z0i2AB\", \"\"\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox6034802\\__tests__\\src\\server\\connecteAccounts\\RemoveConnectedAccountController.spec.ts:30:45)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["22"], "coveredBy": ["22"], "location": {"end": {"column": 31, "line": 19}, "start": {"column": 11, "line": 19}}}, {"id": "42", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: \"a1LDF00000K4z0i2AB\", \"CREATOR\"\nReceived: \"a1LDF00000K4z0i2AB\", \"\"\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox6034802\\__tests__\\src\\server\\connecteAccounts\\RemoveConnectedAccountController.spec.ts:47:45)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["23"], "coveredBy": ["23"], "location": {"end": {"column": 20, "line": 20}, "start": {"column": 11, "line": 20}}}], "source": "import { Service } from \"typedi\";\r\nimport { NextApiResponse } from \"next\";\r\nimport { Controller, NextApiRequestWithSession } from \"@eait-playerexp-cn/server-kernel\";\r\nimport { HttpStatus } from \"@eait-playerexp-cn/http\";\r\nimport ConnectedAccountsHttpClient from \"./ConnectedAccountsHttpClient\";\r\nimport config from \"../../../config\";\r\nimport { AuthenticatedRequestHandler } from \"@eait-playerexp-cn/identity\";\r\n\r\n@Service()\r\nclass RemoveConnectedAccountController extends AuthenticatedRequestHandler implements Controller {\r\n  constructor(private readonly connectedAccounts: ConnectedAccountsHttpClient) {\r\n    super();\r\n  }\r\n\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    const type = config.FLAG_PER_PROGRAM_PROFILE\r\n      ? this.identity(req).type\r\n      : this.hasSession(req, \"nucleusId\")\r\n        ? \"INTERESTED_CREATOR\"\r\n        : \"CREATOR\";\r\n    const accountId = req.query.id as string;\r\n    await this.connectedAccounts.removeConnectedAccount(accountId, type);\r\n    this.empty(res, HttpStatus.OK);\r\n  }\r\n}\r\n\r\nexport default RemoveConnectedAccountController;\r\n"}, "src/server/connecteAccounts/ViewConnectedAccountsController.ts": {"language": "typescript", "mutants": [{"id": "43", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at Object.parse (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox5647543\\__tests__\\src\\server\\connecteAccounts\\ViewConnectedAccountsController.spec.ts:44:17)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["17"], "coveredBy": ["17", "18"], "location": {"end": {"column": 4, "line": 22}, "start": {"column": 85, "line": 13}}}, {"id": "44", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\nExpected: **********\nReceived: undefined\n    at Object.toEqual (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox5647543\\__tests__\\src\\server\\connecteAccounts\\ViewConnectedAccountsController.spec.ts:47:35)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["17"], "coveredBy": ["17", "18"], "location": {"end": {"column": 45, "line": 19}, "start": {"column": 34, "line": 19}}}], "source": "import { NextApiResponse } from \"next\";\r\nimport { Service } from \"typedi\";\r\nimport { Controller, NextApiRequestWithSession, RequestHandler } from \"@eait-playerexp-cn/server-kernel\";\r\nimport ConnectedAccountsHttpClient from \"./ConnectedAccountsHttpClient\";\r\nimport config from \"config\";\r\n\r\n@Service()\r\nclass ViewConnectedAccountsController extends Request<PERSON><PERSON><PERSON> implements Controller {\r\n  constructor(private connectedAccounts: ConnectedAccountsHttpClient) {\r\n    super();\r\n  }\r\n\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    const nucleusId = Number(req.query.nucleusId);\r\n    const accounts = config.INTERESTED_CREATOR_REAPPLY_PERIOD\r\n      ? await this.connectedAccounts.getAllConnectedAccountsWithExpirationStatus(nucleusId)\r\n      : await this.connectedAccounts.getAllConnectedAccounts(nucleusId);\r\n\r\n    await this.addToSession(req, \"nucleusId\", nucleusId);\r\n\r\n    this.json(res, accounts);\r\n  }\r\n}\r\n\r\nexport default ViewConnectedAccountsController;\r\n"}, "src/server/connecteAccounts/ViewRecentErrorsController.ts": {"language": "typescript", "mutants": [{"id": "45", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/connecteAccounts/ViewRecentErrorsController.ts(7,3): error TS2377: Constructors for derived classes must contain a 'super' call.\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["29"], "location": {"end": {"column": 4, "line": 9}, "start": {"column": 17, "line": 7}}}, {"id": "46", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at Object.parse (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\connecteAccounts\\ViewRecentErrorsController.spec.ts:25:17)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["29"], "coveredBy": ["29"], "location": {"end": {"column": 4, "line": 14}, "start": {"column": 85, "line": 11}}}, {"id": "47", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\nExpected: {\"code\": \"view-facebook-pages-invalid-input\", \"message\": \"Please try with appropriate permissions\"}\nReceived: null\n    at Object.toEqual (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\connecteAccounts\\ViewRecentErrorsController.spec.ts:25:40)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["29"], "coveredBy": ["29"], "location": {"end": {"column": 44, "line": 12}, "start": {"column": 37, "line": 12}}}], "source": "import { NextApiResponse } from \"next\";\r\nimport { Service } from \"typedi\";\r\nimport { <PERSON>, NextApiRequestWithSession, RequestHandler } from \"@eait-playerexp-cn/server-kernel\";\r\n\r\n@Service()\r\nclass ViewRecentErrorsController extends RequestHandler implements Controller {\r\n  constructor() {\r\n    super();\r\n  }\r\n\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    const error = this.session(req, \"error\");\r\n    this.json(res, error);\r\n  }\r\n}\r\n\r\nexport default ViewRecentErrorsController;\r\n"}, "src/server/contentScanning/ContentScanningHttpClient.ts": {"language": "typescript", "mutants": [{"id": "48", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/contentScanning/ContentScanningHttpClient.ts(26,56): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["10", "11", "12", "13"], "location": {"end": {"column": 4, "line": 31}, "start": {"column": 83, "line": 26}}}, {"id": "49", "mutatorName": "StringLiteral", "replacement": "``", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n- \"/v1/secure-content?type=INTERESTED_CREATORS\",\n+ \"\",\n  {\"body\": {\"urls\": {\"urls\": [\"https://loremflickr.com/866/1590?lock=3527317293784888\", \"https://picsum.photos/seed/bcrTQZEW8Y/2750/1028\"]}}},\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\contentScanning\\ContentScanningHttpClient.spec.ts:24:25)", "status": "Killed", "testsCompleted": 4, "static": false, "killedBy": ["10"], "coveredBy": ["10", "11", "12", "13"], "location": {"end": {"column": 80, "line": 27}, "start": {"column": 47, "line": 27}}}, {"id": "50", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n  \"/v1/secure-content?type=INTERESTED_CREATORS\",\n- Object {\n-   \"body\": Object {\n-     \"urls\": Object {\n-       \"urls\": Array [\n-         \"https://picsum.photos/seed/jEyZcbJKN/2956/1263\",\n-         \"https://loremflickr.com/682/599?lock=1583129168607932\",\n-       ],\n-     },\n-   },\n- }\n+ Object {},\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox6034802\\__tests__\\src\\server\\contentScanning\\ContentScanningHttpClient.spec.ts:24:25)", "status": "Killed", "testsCompleted": 4, "static": false, "killedBy": ["10"], "coveredBy": ["10", "11", "12", "13"], "location": {"end": {"column": 6, "line": 29}, "start": {"column": 82, "line": 27}}}, {"id": "51", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n  \"/v1/secure-content?type=INTERESTED_CREATORS\",\n  Object {\n-   \"body\": Object {\n-     \"urls\": Object {\n-       \"urls\": Array [\n-         \"https://loremflickr.com/779/1145?lock=3024248392843456\",\n-         \"https://loremflickr.com/1507/3298?lock=1529710995183645\",\n-       ],\n-     },\n-   },\n+   \"body\": Object {},\n  },\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\contentScanning\\ContentScanningHttpClient.spec.ts:24:25)", "status": "Killed", "testsCompleted": 4, "static": false, "killedBy": ["10"], "coveredBy": ["10", "11", "12", "13"], "location": {"end": {"column": 27, "line": 28}, "start": {"column": 13, "line": 28}}}], "source": "import { Inject, Service } from \"typedi\";\r\nimport type { TraceableHttpClient } from \"@eait-playerexp-cn/http-client\";\r\n\r\nexport type ContentUrls = {\r\n  urls: Array<string>;\r\n};\r\n\r\ntype ScannedUrl = {\r\n  url: string;\r\n  isSecure: boolean;\r\n};\r\n\r\nexport type ContentScanResult = {\r\n  results: Array<ScannedUrl>;\r\n};\r\n\r\nexport type ScanType = \"INTERESTED_CREATORS\" | \"CREATORS\";\r\n\r\n@Service()\r\nclass ContentScanningHttpClient {\r\n  constructor(@Inject(\"contentScanningClient\") private readonly client: TraceableHttpClient) {}\r\n\r\n  /**\r\n   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-scanning/content-scanning-api/docs/api.html#operation/validateContentUrl Validate Content URL}\r\n   */\r\n  async verifyUrls(urls: ContentUrls, type: ScanType): Promise<ContentScanResult> {\r\n    const scanResult = await this.client.post(`/v1/secure-content?type=${type}`, {\r\n      body: { urls: urls }\r\n    });\r\n    return Promise.resolve(scanResult.data);\r\n  }\r\n}\r\n\r\nexport default ContentScanningHttpClient;\r\n"}, "src/server/contentScanning/VerifyContentUrlController.ts": {"language": "typescript", "mutants": [{"id": "52", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "Survived", "testsCompleted": 2, "static": false, "killedBy": [], "coveredBy": ["20", "21"], "location": {"end": {"column": 31, "line": 12}, "start": {"column": 11, "line": 12}}}, {"id": "53", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 1\nReceived number of calls: 0\n    at Object.toHaveBeenCalledTimes (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\contentScanning\\VerifyContentUrlController.spec.ts:37:34)", "status": "Killed", "testsCompleted": 2, "static": false, "killedBy": ["20"], "coveredBy": ["20", "21"], "location": {"end": {"column": 4, "line": 20}, "start": {"column": 85, "line": 14}}}], "source": "import { Inject, Service } from \"typedi\";\r\nimport { NextApiResponse } from \"next\";\r\nimport ContentScanning, { ContentUrls, ScanType } from \"./ContentScanningHttpClient\";\r\nimport { Controller, NextApiRequestWithSession, RequestHandler } from \"@eait-playerexp-cn/server-kernel\";\r\n\r\n@Service()\r\nclass VerifyContentUrlController extends RequestHandler implements Controller {\r\n  constructor(\r\n    private readonly contentScanning: ContentScanning,\r\n    @Inject(\"supportedLocales\") supportedLocales: string[]\r\n  ) {\r\n    super({ supportedLocales });\r\n  }\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    const urls = req?.body as unknown as ContentUrls;\r\n    const type = req.query?.type as ScanType;\r\n    const scanResult = await this.contentScanning.verifyUrls(urls, type);\r\n\r\n    this.json(res, scanResult);\r\n  }\r\n}\r\n\r\nexport default VerifyContentUrlController;\r\n"}, "src/server/interestedCreator/AddRequestToJoinProgramController.ts": {"language": "typescript", "mutants": [{"id": "54", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "Survived", "testsCompleted": 1, "static": false, "killedBy": [], "coveredBy": ["25"], "location": {"end": {"column": 31, "line": 14}, "start": {"column": 11, "line": 14}}}, {"id": "55", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(received).toBe(expected) // Object.is equality\n\nExpected: 201\nReceived: 200\n    at Object.toBe (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\AddRequestToJoinProgramController.spec.ts:28:34)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["25"], "coveredBy": ["25"], "location": {"end": {"column": 4, "line": 25}, "start": {"column": 85, "line": 17}}}], "source": "import { Inject, Service } from \"typedi\";\r\nimport { NextApiResponse } from \"next\";\r\nimport InterestedCreatorsHttpClient from \"./InterestedCreatorsHttpClient\";\r\nimport InterestedCreator from \"./InterestedCreator\";\r\nimport { Controller, NextApiRequestWithSession, RequestHandler } from \"@eait-playerexp-cn/server-kernel\";\r\nimport config from \"config\";\r\n\r\n@Service()\r\nclass AddRequestToJoinProgramController extends Request<PERSON>andler implements Controller {\r\n  constructor(\r\n    private readonly interestedCreators: InterestedCreatorsHttpClient,\r\n    @Inject(\"supportedLocales\") supportedLocales: string[]\r\n  ) {\r\n    super({ supportedLocales });\r\n  }\r\n\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    await this.interestedCreators.addRequestToJoinProgram({\r\n      ...req.body,\r\n      creatorProgram: {\r\n        code: config.PROGRAM_CODE\r\n      }\r\n    } as InterestedCreator);\r\n    this.empty(res);\r\n  }\r\n}\r\n\r\nexport default AddRequestToJoinProgramController;\r\n"}, "src/server/interestedCreator/InterestedCreator.ts": {"language": "typescript", "mutants": [{"id": "56", "mutatorName": "ArrayDeclaration", "replacement": "[\"<PERSON><PERSON><PERSON> was here\"]", "statusReason": "src/server/interestedCreator/InterestedCreator.ts(77,44): error TS2322: Type 'string' is not assignable to type 'CreatorType'.\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["14", "15", "16", "19", "24"], "location": {"end": {"column": 45, "line": 77}, "start": {"column": 43, "line": 77}}}, {"id": "57", "mutatorName": "ArrayDeclaration", "replacement": "[\"<PERSON><PERSON><PERSON> was here\"]", "statusReason": "src/server/interestedCreator/InterestedCreator.ts(78,45): error TS2322: Type 'string' is not assignable to type 'Language'.\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["14", "15", "16", "19", "24"], "location": {"end": {"column": 46, "line": 78}, "start": {"column": 44, "line": 78}}}, {"id": "58", "mutatorName": "ArrayDeclaration", "replacement": "[\"<PERSON><PERSON><PERSON> was here\"]", "statusReason": "src/server/interestedCreator/InterestedCreator.ts(79,42): error TS2322: Type 'string' is not assignable to type 'ContentUrl'.\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["14", "15", "16", "19", "24"], "location": {"end": {"column": 43, "line": 79}, "start": {"column": 41, "line": 79}}}, {"id": "59", "mutatorName": "ArrayDeclaration", "replacement": "[\"<PERSON><PERSON><PERSON> was here\"]", "statusReason": "src/server/interestedCreator/InterestedCreator.ts(80,58): error TS2322: Type 'string' is not assignable to type 'PreferredFranchise'.\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["14", "15", "16", "19", "24"], "location": {"end": {"column": 59, "line": 80}, "start": {"column": 57, "line": 80}}}, {"id": "60", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "TypeError: Cannot read properties of undefined (reading 'url')\n    at Object.url (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\InterestedCreator.spec.ts:57:64)\n    at Promise.then.completed (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)", "status": "Killed", "testsCompleted": 5, "static": false, "killedBy": ["14"], "coveredBy": ["14", "15", "16", "19", "24"], "location": {"end": {"column": 4, "line": 87}, "start": {"column": 54, "line": 85}}}, {"id": "61", "mutatorName": "ConditionalExpression", "replacement": "true", "statusReason": "TypeError: Cannot read properties of undefined (reading 'url')\n    at Object.url (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\InterestedCreator.spec.ts:57:64)\n    at Promise.then.completed (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)", "status": "Killed", "testsCompleted": 5, "static": false, "killedBy": ["14"], "coveredBy": ["14", "15", "16", "19", "24"], "location": {"end": {"column": 48, "line": 86}, "start": {"column": 25, "line": 86}}}, {"id": "62", "mutatorName": "ConditionalExpression", "replacement": "false", "statusReason": "TypeError: Cannot read properties of undefined (reading 'url')\n    at Object.url (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\InterestedCreator.spec.ts:57:64)\n    at Promise.then.completed (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)", "status": "Killed", "testsCompleted": 5, "static": false, "killedBy": ["14"], "coveredBy": ["14", "15", "16", "19", "24"], "location": {"end": {"column": 48, "line": 86}, "start": {"column": 25, "line": 86}}}, {"id": "63", "mutatorName": "LogicalOperator", "replacement": "interestedCreator && {}", "statusReason": "TypeError: Cannot read properties of undefined (reading 'value')\n    at InterestedCreator.value [as completeWithAdditionalLinks] (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\src\\server\\interestedCreator\\InterestedCreator.ts:184:35)\n    at InterestedCreatorsHttpClient.completeWithAdditionalLinks [as addRequestToJoinProgram] (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\src\\server\\interestedCreator\\InterestedCreatorsHttpClient.ts:63:77)\n    at Object.addRequestToJoinProgram (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\InterestedCreatorsHttpClient.spec.ts:57:40)\n    at Promise.then.completed (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)", "status": "Killed", "testsCompleted": 5, "static": false, "killedBy": ["19"], "coveredBy": ["14", "15", "16", "19", "24"], "location": {"end": {"column": 48, "line": 86}, "start": {"column": 25, "line": 86}}}, {"id": "64", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/interestedCreator/InterestedCreator.ts(89,15): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["14"], "location": {"end": {"column": 4, "line": 107}, "start": {"column": 53, "line": 89}}}, {"id": "65", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "src/server/interestedCreator/InterestedCreator.ts(90,5): error TS2740: Type '{}' is missing the following properties from type 'CompletedInterestedCreatorApplication': defaultGamerTag, nucleusId, firstName, lastName, and 9 more.\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["14"], "location": {"end": {"column": 6, "line": 106}, "start": {"column": 12, "line": 90}}}, {"id": "66", "mutatorName": "ArrowFunction", "replacement": "() => undefined", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\n- Expected  - 2\n+ Received  + 2\n\n@@ -8,12 +8,12 @@\n    \"contentLanguages\": Array [\n      \"en\",\n    ],\n    \"countryCode\": \"US\",\n    \"creatorTypes\": Array [\n-     \"PODCASTER\",\n-     \"YOUTUBER\",\n+     undefined,\n+     undefined,\n    ],\n    \"dateOfBirth\": 1110047802000,\n    \"defaultGamerTag\": undefined,\n    \"firstName\": \"Candido\",\n    \"lastName\": \"Pouros-O'Kon\",\n    at Object.toEqual (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\InterestedCreator.spec.ts:65:34)\n    at Promise.then.completed (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["14"], "coveredBy": ["14"], "location": {"end": {"column": 77, "line": 99}, "start": {"column": 43, "line": 99}}}, {"id": "67", "mutatorName": "ArrowFunction", "replacement": "() => undefined", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\n- Expected  - 1\n+ Received  + 1\n\n@@ -4,11 +4,11 @@\n        \"followersCount\": 90,\n        \"uri\": \"https://loremflickr.com/2193/2961?lock=1660498083987809\",\n      },\n    ],\n    \"contentLanguages\": Array [\n-     \"en\",\n+     undefined,\n    ],\n    \"countryCode\": \"US\",\n    \"creatorTypes\": Array [\n      \"LIVE_STREAMER\",\n      \"LIVE_STREAMER\",\n    at Object.toEqual (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\InterestedCreator.spec.ts:65:34)\n    at Promise.then.completed (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["14"], "coveredBy": ["14"], "location": {"end": {"column": 79, "line": 100}, "start": {"column": 51, "line": 100}}}, {"id": "68", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/interestedCreator/InterestedCreator.ts(101,7): error TS2322: Type 'void[]' is not assignable to type 'ContentAccount[]'.\r\n  Type 'void' is not assignable to type 'ContentAccount'.\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["14"], "location": {"end": {"column": 8, "line": 103}, "start": {"column": 54, "line": 101}}}, {"id": "69", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "src/server/interestedCreator/InterestedCreator.ts(101,7): error TS2322: Type '{}[]' is not assignable to type 'ContentAccount[]'.\r\n  Type '{}' is missing the following properties from type 'ContentAccount': uri, followersCount\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["14"], "location": {"end": {"column": 63, "line": 102}, "start": {"column": 16, "line": 102}}}, {"id": "70", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/interestedCreator/InterestedCreator.ts(109,34): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["15", "16", "19"], "location": {"end": {"column": 4, "line": 126}, "start": {"column": 91, "line": 109}}}, {"id": "71", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "src/server/interestedCreator/InterestedCreator.ts(110,5): error TS2740: Type '{}' is missing the following properties from type 'CompletedInterestedCreatorApplicationWithAdditionalLinks': defaultGamerTag, nucleusId, firstName, lastName, and 10 more.\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["15", "16", "19"], "location": {"end": {"column": 6, "line": 125}, "start": {"column": 12, "line": 110}}}, {"id": "72", "mutatorName": "ArrowFunction", "replacement": "() => undefined", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n  \"/v4/creator-applications\",\n@@ -9,12 +9,12 @@\n      \"countryCode\": \"US\",\n      \"creatorProgram\": Object {\n        \"code\": \"affiliate\",\n      },\n      \"creatorTypes\": Array [\n-       \"YOUTUBER\",\n-       \"PODCASTER\",\n+       undefined,\n+       undefined,\n      ],\n      \"dateOfBirth\": 1110047833000,\n      \"defaultGamerTag\": undefined,\n      \"firstName\": \"Jerod\",\n      \"lastName\": \"Towne\",,\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\InterestedCreatorsHttpClient.spec.ts:63:25)", "status": "Killed", "testsCompleted": 3, "static": false, "killedBy": ["19"], "coveredBy": ["15", "16", "19"], "location": {"end": {"column": 77, "line": 119}, "start": {"column": 43, "line": 119}}}, {"id": "73", "mutatorName": "ArrowFunction", "replacement": "() => undefined", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\n- Expected  - 1\n+ Received  + 1\n\n@@ -1,11 +1,11 @@\n  Object {\n    \"additionalLinks\": Array [\n      \"https://picsum.photos/seed/ZroAtt/625/2005\",\n    ],\n    \"contentLanguages\": Array [\n-     \"en\",\n+     undefined,\n    ],\n    \"countryCode\": \"US\",\n    \"creatorProgram\": Object {\n      \"code\": \"affiliate\",\n    },\n    at Object.toEqual (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\InterestedCreator.spec.ts:82:34)\n    at Promise.then.completed (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)", "status": "Killed", "testsCompleted": 3, "static": false, "killedBy": ["15"], "coveredBy": ["15", "16", "19"], "location": {"end": {"column": 79, "line": 120}, "start": {"column": 51, "line": 120}}}, {"id": "74", "mutatorName": "MethodExpression", "replacement": "this.contentUrls", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\n- Expected  - 1\n+ Received  + 3\n\n@@ -1,7 +1,9 @@\n  Object {\n-   \"additionalLinks\": Array [],\n+   \"additionalLinks\": Array [\n+     \"https://\",\n+   ],\n    \"contentLanguages\": Array [\n      \"en\",\n    ],\n    \"countryCode\": \"US\",\n    \"creatorProgram\": Object {\n    at Object.toEqual (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\InterestedCreator.spec.ts:99:34)\n    at Promise.then.completed (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)", "status": "Killed", "testsCompleted": 3, "static": false, "killedBy": ["16"], "coveredBy": ["15", "16", "19"], "location": {"end": {"column": 87, "line": 121}, "start": {"column": 24, "line": 121}}}, {"id": "75", "mutatorName": "ArrowFunction", "replacement": "() => undefined", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n  \"/v4/creator-applications\",\n@@ -1,10 +1,8 @@\n  Object {\n    \"body\": Object {\n-     \"additionalLinks\": Array [\n-       \"https://picsum.photos/seed/IJwuRZygt/1268/998\",\n-     ],\n+     \"additionalLinks\": Array [],\n      \"contentLanguages\": Array [\n        \"en\",\n      ],\n      \"countryCode\": \"US\",\n      \"creatorProgram\": Object {,\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\InterestedCreatorsHttpClient.spec.ts:63:25)", "status": "Killed", "testsCompleted": 3, "static": false, "killedBy": ["19"], "coveredBy": ["15", "16", "19"], "location": {"end": {"column": 86, "line": 121}, "start": {"column": 48, "line": 121}}}, {"id": "76", "mutatorName": "ConditionalExpression", "replacement": "true", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\n- Expected  - 1\n+ Received  + 3\n\n@@ -1,7 +1,9 @@\n  Object {\n-   \"additionalLinks\": Array [],\n+   \"additionalLinks\": Array [\n+     \"https://\",\n+   ],\n    \"contentLanguages\": Array [\n      \"en\",\n    ],\n    \"countryCode\": \"US\",\n    \"creatorProgram\": Object {\n    at Object.toEqual (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\InterestedCreator.spec.ts:99:34)\n    at Promise.then.completed (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)", "status": "Killed", "testsCompleted": 3, "static": false, "killedBy": ["16"], "coveredBy": ["15", "16", "19"], "location": {"end": {"column": 86, "line": 121}, "start": {"column": 61, "line": 121}}}, {"id": "77", "mutatorName": "ConditionalExpression", "replacement": "false", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\n- Expected  - 3\n+ Received  + 1\n\n@@ -1,9 +1,7 @@\n  Object {\n-   \"additionalLinks\": Array [\n-     \"https://loremflickr.com/1225/2676?lock=4984909697317228\",\n-   ],\n+   \"additionalLinks\": Array [],\n    \"contentLanguages\": Array [\n      \"en\",\n    ],\n    \"countryCode\": \"US\",\n    \"creatorProgram\": Object {\n    at Object.toEqual (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\InterestedCreator.spec.ts:82:34)\n    at Promise.then.completed (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)", "status": "Killed", "testsCompleted": 3, "static": false, "killedBy": ["15"], "coveredBy": ["15", "16", "19"], "location": {"end": {"column": 86, "line": 121}, "start": {"column": 61, "line": 121}}}, {"id": "78", "mutatorName": "LogicalOperator", "replacement": "url || url !== \"https://\"", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\n- Expected  - 1\n+ Received  + 3\n\n@@ -1,7 +1,9 @@\n  Object {\n-   \"additionalLinks\": Array [],\n+   \"additionalLinks\": Array [\n+     \"https://\",\n+   ],\n    \"contentLanguages\": Array [\n      \"en\",\n    ],\n    \"countryCode\": \"US\",\n    \"creatorProgram\": Object {\n    at Object.toEqual (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\InterestedCreator.spec.ts:99:34)\n    at Promise.then.completed (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)", "status": "Killed", "testsCompleted": 3, "static": false, "killedBy": ["16"], "coveredBy": ["15", "16", "19"], "location": {"end": {"column": 86, "line": 121}, "start": {"column": 61, "line": 121}}}, {"id": "79", "mutatorName": "ConditionalExpression", "replacement": "true", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\n- Expected  - 1\n+ Received  + 3\n\n@@ -1,7 +1,9 @@\n  Object {\n-   \"additionalLinks\": Array [],\n+   \"additionalLinks\": Array [\n+     \"https://\",\n+   ],\n    \"contentLanguages\": Array [\n      \"en\",\n    ],\n    \"countryCode\": \"US\",\n    \"creatorProgram\": Object {\n    at Object.toEqual (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\InterestedCreator.spec.ts:99:34)\n    at Promise.then.completed (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)", "status": "Killed", "testsCompleted": 3, "static": false, "killedBy": ["16"], "coveredBy": ["15", "16", "19"], "location": {"end": {"column": 86, "line": 121}, "start": {"column": 68, "line": 121}}}, {"id": "80", "mutatorName": "EqualityOperator", "replacement": "url === \"https://\"", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\n- Expected  - 3\n+ Received  + 1\n\n@@ -1,9 +1,7 @@\n  Object {\n-   \"additionalLinks\": Array [\n-     \"https://picsum.photos/seed/vvYt8ztM/2681/74\",\n-   ],\n+   \"additionalLinks\": Array [],\n    \"contentLanguages\": Array [\n      \"en\",\n    ],\n    \"countryCode\": \"US\",\n    \"creatorProgram\": Object {\n    at Object.toEqual (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\InterestedCreator.spec.ts:82:34)\n    at Promise.then.completed (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)", "status": "Killed", "testsCompleted": 3, "static": false, "killedBy": ["15"], "coveredBy": ["15", "16", "19"], "location": {"end": {"column": 86, "line": 121}, "start": {"column": 68, "line": 121}}}, {"id": "81", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\n- Expected  - 1\n+ Received  + 3\n\n@@ -1,7 +1,9 @@\n  Object {\n-   \"additionalLinks\": Array [],\n+   \"additionalLinks\": Array [\n+     \"https://\",\n+   ],\n    \"contentLanguages\": Array [\n      \"en\",\n    ],\n    \"countryCode\": \"US\",\n    \"creatorProgram\": Object {\n    at Object.toEqual (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\InterestedCreator.spec.ts:99:34)\n    at Promise.then.completed (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)", "status": "Killed", "testsCompleted": 3, "static": false, "killedBy": ["16"], "coveredBy": ["15", "16", "19"], "location": {"end": {"column": 86, "line": 121}, "start": {"column": 76, "line": 121}}}, {"id": "82", "mutatorName": "ArrowFunction", "replacement": "() => undefined", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\n- Expected  - 1\n+ Received  + 1\n\n@@ -1,8 +1,8 @@\n  Object {\n    \"additionalLinks\": Array [\n-     \"https://loremflickr.com/216/1911?lock=3617103329239582\",\n+     undefined,\n    ],\n    \"contentLanguages\": Array [\n      \"en\",\n    ],\n    \"countryCode\": \"US\",\n    at Object.toEqual (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\InterestedCreator.spec.ts:82:34)\n    at Promise.then.completed (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\node_modules\\jest-runner\\build\\runTest.js:444:34)", "status": "Killed", "testsCompleted": 3, "static": false, "killedBy": ["15"], "coveredBy": ["15", "16", "19"], "location": {"end": {"column": 108, "line": 121}, "start": {"column": 92, "line": 121}}}], "source": "type Country = {\r\n  value: string;\r\n  label: string;\r\n  name: string;\r\n};\r\ntype Language = {\r\n  value: string;\r\n  label: string;\r\n  id: string;\r\n};\r\nexport type ContentUrl = {\r\n  url: string;\r\n  followers: number;\r\n};\r\ntype ContentAccount = {\r\n  uri: string;\r\n  followersCount: number;\r\n};\r\nexport type FranchiseType = \"PRIMARY\" | \"SECONDARY\";\r\nexport type PreferredFranchise = {\r\n  id: string;\r\n  type: FranchiseType;\r\n};\r\ntype CreatorType = {\r\n  value: string;\r\n  imageAsIcon: string;\r\n  label: string;\r\n  checked: boolean;\r\n};\r\ntype PreferredLanguage = {\r\n  code: string;\r\n  name: string;\r\n};\r\ntype CreatorProgram = {\r\n  code: string;\r\n};\r\ntype CompletedInterestedCreatorApplication = {\r\n  defaultGamerTag: string;\r\n  nucleusId: number;\r\n  firstName: string;\r\n  lastName: string;\r\n  originEmail: string;\r\n  dateOfBirth: string | number;\r\n  preferredEmail: string;\r\n  countryCode: string;\r\n  creatorTypes: string[];\r\n  contentLanguages: string[];\r\n  contentAccounts: ContentAccount[];\r\n  preferredFranchises: PreferredFranchise[];\r\n  preferredLanguage: PreferredLanguage;\r\n};\r\ntype CompletedInterestedCreatorApplicationWithAdditionalLinks = {\r\n  defaultGamerTag: string;\r\n  nucleusId: number;\r\n  firstName: string;\r\n  lastName: string;\r\n  originEmail: string;\r\n  dateOfBirth: string | number;\r\n  preferredEmail: string;\r\n  countryCode: string;\r\n  creatorTypes: string[];\r\n  contentLanguages: string[];\r\n  additionalLinks: Array<string>;\r\n  preferredFranchises: PreferredFranchise[];\r\n  preferredLanguage: PreferredLanguage;\r\n  creatorProgram: CreatorProgram;\r\n};\r\nclass InterestedCreator {\r\n  readonly defaultGamerTag: string;\r\n  readonly nucleusId: number;\r\n  readonly firstName?: string;\r\n  readonly lastName?: string;\r\n  readonly originEmail: string;\r\n  readonly dateOfBirth: string | number;\r\n  readonly preferredEmail?: string;\r\n  readonly country?: Country;\r\n  readonly creatorTypes?: CreatorType[] = [];\r\n  readonly contentLanguages?: Language[] = [];\r\n  readonly contentUrls?: ContentUrl[] = [];\r\n  readonly preferredFranchises?: PreferredFranchise[] = [];\r\n  readonly preferredLanguage?: PreferredLanguage;\r\n  readonly analyticsId: string;\r\n  readonly creatorProgram: CreatorProgram;\r\n\r\n  constructor(interestedCreator?: InterestedCreator) {\r\n    Object.assign(this, interestedCreator || {});\r\n  }\r\n\r\n  complete(): CompletedInterestedCreatorApplication {\r\n    return {\r\n      defaultGamerTag: this.defaultGamerTag,\r\n      nucleusId: this.nucleusId,\r\n      firstName: this.firstName,\r\n      lastName: this.lastName,\r\n      originEmail: this.originEmail,\r\n      dateOfBirth: this.dateOfBirth,\r\n      preferredEmail: this.preferredEmail,\r\n      countryCode: this.country.value,\r\n      creatorTypes: this.creatorTypes.map((creatorType) => creatorType.value),\r\n      contentLanguages: this.contentLanguages.map((language) => language.value),\r\n      contentAccounts: this.contentUrls.map((url) => {\r\n        return { uri: url.url, followersCount: url.followers };\r\n      }),\r\n      preferredFranchises: this.preferredFranchises,\r\n      preferredLanguage: this.preferredLanguage\r\n    };\r\n  }\r\n\r\n  completeWithAdditionalLinks(): CompletedInterestedCreatorApplicationWithAdditionalLinks {\r\n    return {\r\n      defaultGamerTag: this.defaultGamerTag,\r\n      nucleusId: this.nucleusId,\r\n      firstName: this.firstName,\r\n      lastName: this.lastName,\r\n      originEmail: this.originEmail,\r\n      dateOfBirth: this.dateOfBirth,\r\n      preferredEmail: this.preferredEmail,\r\n      countryCode: this.country.value,\r\n      creatorTypes: this.creatorTypes.map((creatorType) => creatorType.value),\r\n      contentLanguages: this.contentLanguages.map((language) => language.value),\r\n      additionalLinks: this.contentUrls.filter(({ url }) => url && url !== \"https://\").map(({ url }) => url),\r\n      preferredFranchises: this.preferredFranchises,\r\n      preferredLanguage: this.preferredLanguage,\r\n      creatorProgram: this.creatorProgram\r\n    };\r\n  }\r\n}\r\n\r\nexport default InterestedCreator;\r\n"}, "src/server/interestedCreator/InterestedCreatorsHttpClient.ts": {"language": "typescript", "mutants": [{"id": "83", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 1\nReceived number of calls: 0\n    at Object.toHaveBeenCalledTimes (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\InterestedCreatorsHttpClient.spec.ts:62:25)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["19"], "coveredBy": ["19"], "location": {"end": {"column": 4, "line": 14}, "start": {"column": 86, "line": 11}}}, {"id": "84", "mutatorName": "StringLiteral", "replacement": "``", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n- \"/v4/creator-applications\",\n+ \"\",\n  {\"body\": {\"additionalLinks\": [\"https://picsum.photos/seed/BCazc2D/2611/2414\"], \"contentLanguages\": [\"en\"], \"countryCode\": \"US\", \"creatorProgram\": {\"code\": \"affiliate\"}, \"creatorTypes\": [\"LIVE_STREAMER\", \"PODCASTER\"], \"dateOfBirth\": 1110047907000, \"defaultGamerTag\": undefined, \"firstName\": \"Abbigail\", \"lastName\": \"Batz\", \"nucleusId\": 3935564906889451, \"originEmail\": \"<EMAIL>\", \"preferredEmail\": \"<EMAIL>\", \"preferredFranchises\": [{\"boxArtUrl\": \"https://loremflickr.com/1865/1997?lock=6200956691906458\", \"id\": \"431bf284-de90-4609-8a87-2e3dae447f5d\", \"label\": \"Need for Speed\", \"name\": \"Need for Speed\", \"type\": \"PRIMARY\", \"value\": \"f248761b-22e7-4576-935a-02c6595b01e8\"}, {\"boxArtUrl\": \"https://loremflickr.com/1915/2160?lock=911620192613822\", \"id\": \"2bb9bde5-d651-42d0-86ce-4b300a300cd3\", \"label\": \"Madden NFL\", \"name\": \"Madden NFL\", \"type\": \"SECONDARY\", \"value\": \"148f76a5-ae82-442a-b028-effa92c62e5a\"}], \"preferredLanguage\": {\"code\": \"en_US\", \"label\": \"Canada\", \"name\": \"English\", \"value\": \"JO\"}}},\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\InterestedCreatorsHttpClient.spec.ts:63:25)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["19"], "coveredBy": ["19"], "location": {"end": {"column": 54, "line": 13}, "start": {"column": 28, "line": 13}}}, {"id": "85", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\n- Expected\n+ Received\n\n  \"/v4/creator-applications\",\n- Object {\n-   \"body\": Object {\n-     \"additionalLinks\": Array [\n-       \"https://loremflickr.com/2091/1309?lock=1418954620417666\",\n-     ],\n-     \"contentLanguages\": Array [\n-       \"en\",\n-     ],\n-     \"countryCode\": \"US\",\n-     \"creatorProgram\": Object {\n-       \"code\": \"affiliate\",\n-     },\n-     \"creatorTypes\": Array [\n-       \"PODCASTER\",\n-       \"BLOGGER\",\n-     ],\n-     \"dateOfBirth\": 1110047911000,\n-     \"defaultGamerTag\": undefined,\n-     \"firstName\": \"Shaylee\",\n-     \"lastName\": \"Rau\",\n-     \"nucleusId\": 2424558552649747,\n-     \"originEmail\": \"<EMAIL>\",\n-     \"preferredEmail\": \"<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>@gmail.com\",\n-     \"preferredFranchises\": Array [\n-       Object {\n-         \"boxArtUrl\": \"https://loremflickr.com/2849/2302?lock=6069885913785913\",\n-         \"id\": \"2a8dbf9a-2ddd-4dba-80b7-da624e18c5b0\",\n-         \"label\": \"Need for Speed\",\n-         \"name\": \"Need for Speed\",\n-         \"type\": \"PRIMARY\",\n-         \"value\": \"38b8b8f3-218e-4291-91aa-80248659faed\",\n-       },\n-       Object {\n-         \"boxArtUrl\": \"https://loremflickr.com/3745/1787?lock=8914536897513880\",\n-         \"id\": \"bec34243-0b55-4b69-855e-f2f2478c8432\",\n-         \"label\": \"Apex Legends\",\n-         \"name\": \"Apex Legends\",\n-         \"type\": \"SECONDARY\",\n-         \"value\": \"b0939881-18f0-496f-b447-ad0d91a75c95\",\n-       },\n-     ],\n-     \"preferredLanguage\": Object {\n-       \"code\": \"en_US\",\n-       \"label\": \"United States\",\n-       \"name\": \"English\",\n-       \"value\": \"AX\",\n-     },\n-   },\n- }\n+ Object {},\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\InterestedCreatorsHttpClient.spec.ts:63:25)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["19"], "coveredBy": ["19"], "location": {"end": {"column": 86, "line": 13}, "start": {"column": 56, "line": 13}}}], "source": "import { Inject, Service } from \"typedi\";\r\nimport type { TraceableHttpClient } from \"@eait-playerexp-cn/http-client\";\r\nimport InterestedCreator from \"./InterestedCreator\";\r\n\r\n@Service()\r\nclass InterestedCreatorsHttpClient {\r\n  constructor(@Inject(\"operationsClient\") private readonly client: TraceableHttpClient) {}\r\n  /**\r\n   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Interested-Creators/operation/submitInterestedCreatorApplicationV4}\r\n   */\r\n  async addRequestToJoinProgram(interestedCreator: InterestedCreator): Promise<void> {\r\n    const completedApplication = new InterestedCreator(interestedCreator).completeWithAdditionalLinks();\r\n    await this.client.post(`/v4/creator-applications`, { body: completedApplication });\r\n  }\r\n}\r\n\r\nexport default InterestedCreatorsHttpClient;\r\n"}, "src/server/interestedCreator/StartApplicationController.ts": {"language": "typescript", "mutants": [{"id": "86", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/interestedCreator/StartApplicationController.ts(7,3): error TS2377: Constructors for derived classes must contain a 'super' call.\r\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["28"], "location": {"end": {"column": 4, "line": 9}, "start": {"column": 71, "line": 7}}}, {"id": "87", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "Survived", "testsCompleted": 1, "static": false, "killedBy": [], "coveredBy": ["28"], "location": {"end": {"column": 31, "line": 8}, "start": {"column": 11, "line": 8}}}, {"id": "88", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: 302, \"http://localhost:3040/support-a-creator/api/login\"\n\nNumber of calls: 0\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox6034802\\__tests__\\src\\server\\interestedCreator\\StartApplicationController.spec.ts:28:26)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["28"], "coveredBy": ["28"], "location": {"end": {"column": 4, "line": 13}, "start": {"column": 85, "line": 10}}}, {"id": "89", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\nExpected: true\nReceived: undefined\n    at Object.toEqual (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox6034802\\__tests__\\src\\server\\interestedCreator\\StartApplicationController.spec.ts:29:43)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["28"], "coveredBy": ["28"], "location": {"end": {"column": 53, "line": 11}, "start": {"column": 34, "line": 11}}}, {"id": "90", "mutatorName": "<PERSON>olean<PERSON>iter<PERSON>", "replacement": "false", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\nExpected: true\nReceived: false\n    at Object.toEqual (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox6034802\\__tests__\\src\\server\\interestedCreator\\StartApplicationController.spec.ts:29:43)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["28"], "coveredBy": ["28"], "location": {"end": {"column": 59, "line": 11}, "start": {"column": 55, "line": 11}}}, {"id": "91", "mutatorName": "StringLiteral", "replacement": "``", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)\n\nExpected: 302, \"http://localhost:3040/support-a-creator/api/login\"\nReceived: 302, \"\"\n\nNumber of calls: 1\n    at Object.toHaveBeenCalledWith (C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend\\strykerTmp\\sandbox6034802\\__tests__\\src\\server\\interestedCreator\\StartApplicationController.spec.ts:28:26)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["28"], "coveredBy": ["28"], "location": {"end": {"column": 60, "line": 12}, "start": {"column": 26, "line": 12}}}], "source": "import { Inject, Service } from \"typedi\";\r\nimport { NextApiResponse } from \"next\";\r\nimport { Controller, NextApiRequestWithSession, RequestHandler } from \"@eait-playerexp-cn/server-kernel\";\r\nimport config from \"config\";\r\n@Service()\r\nexport default class StartApplicationController extends RequestHandler implements Controller {\r\n  constructor(@Inject(\"supportedLocales\") supportedLocales: string[]) {\r\n    super({ supportedLocales });\r\n  }\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    await this.addToSession(req, \"interestedCreator\", true);\r\n    this.redirectTo(res, `${config.REDIRECT_URL}/api/login`);\r\n  }\r\n}\r\n"}, "src/server/interestedCreator/UpdateApplicationController.ts": {"language": "typescript", "mutants": [{"id": "92", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/interestedCreator/UpdateApplicationController.ts(8,3): error TS2377: Constructors for derived classes must contain a 'super' call.\n", "status": "CompileError", "static": false, "killedBy": [], "coveredBy": ["24"], "location": {"end": {"column": 4, "line": 10}, "start": {"column": 71, "line": 8}}}, {"id": "93", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "Survived", "testsCompleted": 1, "static": false, "killedBy": [], "coveredBy": ["24"], "location": {"end": {"column": 31, "line": 9}, "start": {"column": 11, "line": 9}}}, {"id": "94", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at Object.parse (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\UpdateApplicationController.spec.ts:47:17)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["24"], "coveredBy": ["24"], "location": {"end": {"column": 4, "line": 25}, "start": {"column": 85, "line": 11}}}, {"id": "95", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "Survived", "testsCompleted": 1, "static": false, "killedBy": [], "coveredBy": ["24"], "location": {"end": {"column": 73, "line": 12}, "start": {"column": 54, "line": 12}}}, {"id": "96", "mutatorName": "<PERSON>olean<PERSON>iter<PERSON>", "replacement": "savedInterestedCreator", "status": "Survived", "testsCompleted": 1, "static": false, "killedBy": [], "coveredBy": ["24"], "location": {"end": {"column": 54, "line": 13}, "start": {"column": 31, "line": 13}}}, {"id": "97", "mutatorName": "ObjectLiteral", "replacement": "{}", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\n- Expected  - 29\n+ Received  +  1\n\n- Object {\n-   \"contentAccounts\": Array [],\n-   \"contentLanguages\": Array [\n-     Object {\n-       \"id\": \"afea9a52-cb15-4ba9-b2c6-706ec8d1cd15\",\n-       \"label\": \"English\",\n-       \"value\": \"en\",\n-     },\n-   ],\n-   \"contentUrls\": Array [\n-     Object {\n-       \"followers\": 3456789,\n-       \"url\": \"https://youtube.com\",\n-     },\n-   ],\n-   \"country\": r {\n-     \"label\": \"Canada\",\n-     \"name\": \"Canada\",\n-     \"value\": undefined,\n-   },\n-   \"creatorTypes\": Array [],\n-   \"dateOfBirth\": \"06/03/2007\",\n-   \"defaultGamerTag\": \"RiffleShooter\",\n-   \"firstName\": \"Jane\",\n-   \"lastName\": \"Doe\",\n-   \"nucleusId\": *********,\n-   \"originEmail\": \"<EMAIL>\",\n-   \"preferredFranchises\": Array [],\n- }\n+ Object {}\n    at Object.toEqual (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\UpdateApplicationController.spec.ts:47:40)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["24"], "coveredBy": ["24"], "location": {"end": {"column": 65, "line": 16}, "start": {"column": 28, "line": 16}}}, {"id": "98", "mutatorName": "ConditionalExpression", "replacement": "true", "statusReason": "Error: expect(jest.fn()).toHaveBeenCalledTimes(expected)\n\nExpected number of calls: 1\nReceived number of calls: 2\n    at Object.toHaveBeenCalledTimes (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\UpdateApplicationController.spec.ts:49:30)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["24"], "coveredBy": ["24"], "location": {"end": {"column": 46, "line": 19}, "start": {"column": 9, "line": 19}}}, {"id": "99", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "Survived", "testsCompleted": 1, "static": false, "killedBy": [], "coveredBy": ["24"], "location": {"end": {"column": 46, "line": 19}, "start": {"column": 9, "line": 19}}}, {"id": "100", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "Survived", "testsCompleted": 1, "static": false, "killedBy": [], "coveredBy": ["24"], "location": {"end": {"column": 45, "line": 19}, "start": {"column": 27, "line": 19}}}, {"id": "101", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 6, "line": 21}, "start": {"column": 48, "line": 19}}}, {"id": "102", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "killedBy": [], "coveredBy": [], "location": {"end": {"column": 59, "line": 20}, "start": {"column": 41, "line": 20}}}, {"id": "103", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "Error: expect(received).toEqual(expected) // deep equality\n\n- Expected  - 29\n+ Received  +  1\n\n- Object {\n-   \"contentAccounts\": Array [],\n-   \"contentLanguages\": Array [\n-     Object {\n-       \"id\": \"967e30f6-8084-434e-a25f-06d8f4657d6a\",\n-       \"label\": \"English\",\n-       \"value\": \"en\",\n-     },\n-   ],\n-   \"contentUrls\": Array [\n-     Object {\n-       \"followers\": 3456789,\n-       \"url\": \"https://youtube.com\",\n-     },\n-   ],\n-   \"country\": r {\n-     \"label\": \"Canada\",\n-     \"name\": \"Canada\",\n-     \"value\": undefined,\n-   },\n-   \"creatorTypes\": Array [],\n-   \"dateOfBirth\": \"06/03/2007\",\n-   \"defaultGamerTag\": \"RiffleShooter\",\n-   \"firstName\": \"Jane\",\n-   \"lastName\": \"Doe\",\n-   \"nucleusId\": *********,\n-   \"originEmail\": \"<EMAIL>\",\n-   \"preferredFranchises\": Array [],\n- }\n+ Object {}\n    at Object.toEqual (C:\\Users\\<USER>\\Desktop\\CN\\Interested Creators\\interested-creator-micro-frontend\\strykerTmp\\sandbox2127230\\__tests__\\src\\server\\interestedCreator\\UpdateApplicationController.spec.ts:48:43)", "status": "Killed", "testsCompleted": 1, "static": false, "killedBy": ["24"], "coveredBy": ["24"], "location": {"end": {"column": 53, "line": 22}, "start": {"column": 34, "line": 22}}}], "source": "import { NextApiResponse } from \"next\";\r\nimport { Inject, Service } from \"typedi\";\r\nimport InterestedCreator from \"./InterestedCreator\";\r\nimport { Controller, NextApiRequestWithSession, RequestHandler } from \"@eait-playerexp-cn/server-kernel\";\r\n\r\n@Service()\r\nexport default class UpdateApplicationController extends RequestH<PERSON>ler implements Controller {\r\n  constructor(@Inject(\"supportedLocales\") supportedLocales: string[]) {\r\n    super({ supportedLocales });\r\n  }\r\n  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {\r\n    const savedInterestedCreator = this.session(req, \"interestedCreator\");\r\n    const interestedCreator = !savedInterestedCreator\r\n      ? new InterestedCreator()\r\n      : new InterestedCreator(savedInterestedCreator as InterestedCreator);\r\n    const updatedCreator = { ...interestedCreator, ...req.body };\r\n    // Removing creatorNucleusId from session as nucleusId is no longer required in applicant submission.\r\n    // We can improve session cookie storage by removing unused session keys.\r\n    if (this.session(req, \"creatorNucleusId\")) {\r\n      await this.removeFromSession(req, \"creatorNucleusId\");\r\n    }\r\n    await this.addToSession(req, \"interestedCreator\", updatedCreator);\r\n\r\n    this.json(res, updatedCreator);\r\n  }\r\n}\r\n"}, "src/shared/headers/ApplicationHeadersProvider.ts": {"language": "typescript", "mutants": [{"id": "src/shared/headers/ApplicationHeadersProvider.ts@4:34-11:3\nBlockStatement: {}", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/shared/headers/ApplicationHeadersProvider.ts(5,14): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\r\n", "status": "CompileError", "static": false, "relativeFileName": "src/shared/headers/ApplicationHeadersProvider.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 12}, "start": {"column": 35, "line": 5}}}, {"id": "src/shared/headers/ApplicationHeadersProvider.ts@5:27-10:5\nObjectLiteral: {}", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/headers/ApplicationHeadersProvider.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 6, "line": 11}, "start": {"column": 28, "line": 6}}}, {"id": "src/shared/headers/ApplicationHeadersProvider.ts@6:14-6:32\nStringLiteral: \"\"", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/headers/ApplicationHeadersProvider.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 33, "line": 7}, "start": {"column": 15, "line": 7}}}, {"id": "src/shared/headers/ApplicationHeadersProvider.ts@7:22-7:40\nStringLiteral: \"\"", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/headers/ApplicationHeadersProvider.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 41, "line": 8}, "start": {"column": 23, "line": 8}}}, {"id": "src/shared/headers/ApplicationHeadersProvider.ts@9:21-9:35\nStringLiteral: \"\"", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/headers/ApplicationHeadersProvider.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 36, "line": 10}, "start": {"column": 22, "line": 10}}}], "source": "import { HeadersProvider, HttpHeaders } from \"@eait-playerexp-cn/http-client\";\r\nimport { MDC } from \"@eait-playerexp-cn/activity-logger\";\r\n\r\nexport default class ApplicationHeadersProvider implements HeadersProvider {\r\n  headers(): Promise<HttpHeaders> {\r\n    return Promise.resolve({\r\n      Accept: \"application/json\",\r\n      \"Content-Type\": \"application/json\",\r\n      \"x-user-id\": MDC.get(\"userId\") as string,\r\n      \"x-client-id\": \"metadata-api\"\r\n    });\r\n  }\r\n}\r\n"}, "src/shared/logging/ApplicationActivity.ts": {"language": "typescript", "mutants": [{"id": "src/shared/logging/ApplicationActivity.ts@5:85-10:3\nBlockStatement: {}", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/shared/logging/ApplicationActivity.ts(6,77): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\r\n", "status": "CompileError", "static": false, "relativeFileName": "src/shared/logging/ApplicationActivity.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 11}, "start": {"column": 86, "line": 6}}}, {"id": "src/shared/logging/ApplicationActivity.ts@6:26-6:45\nStringLiteral: \"\"", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/logging/ApplicationActivity.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 46, "line": 7}, "start": {"column": 27, "line": 7}}}, {"id": "src/shared/logging/ApplicationActivity.ts@6:47-6:85\nConditionalExpression: true", "mutatorName": "ConditionalExpression", "replacement": "true", "statusReason": "src/shared/logging/ApplicationActivity.ts(7,48): error TS2345: Argument of type 'boolean' is not assignable to parameter of type 'string'.\r\n", "status": "CompileError", "static": false, "relativeFileName": "src/shared/logging/ApplicationActivity.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 86, "line": 7}, "start": {"column": 48, "line": 7}}}, {"id": "src/shared/logging/ApplicationActivity.ts@6:47-6:85\nConditionalExpression: false", "mutatorName": "ConditionalExpression", "replacement": "false", "statusReason": "src/shared/logging/ApplicationActivity.ts(7,48): error TS2345: Argument of type 'boolean' is not assignable to parameter of type 'string'.\r\n", "status": "CompileError", "static": false, "relativeFileName": "src/shared/logging/ApplicationActivity.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 86, "line": 7}, "start": {"column": 48, "line": 7}}}, {"id": "src/shared/logging/ApplicationActivity.ts@6:47-6:85\nLogicalOperator: error.message && \"No message provided\"", "mutatorName": "LogicalOperator", "replacement": "error.message && \"No message provided\"", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/logging/ApplicationActivity.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 86, "line": 7}, "start": {"column": 48, "line": 7}}}, {"id": "src/shared/logging/ApplicationActivity.ts@6:64-6:85\nStringLiteral: \"\"", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/logging/ApplicationActivity.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 86, "line": 7}, "start": {"column": 65, "line": 7}}}, {"id": "src/shared/logging/ApplicationActivity.ts@6:87-9:5\nObjectLiteral: {}", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/logging/ApplicationActivity.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 6, "line": 10}, "start": {"column": 88, "line": 7}}}], "source": "import { AxiosError } from \"axios\";\r\nimport { Activity } from \"@eait-playerexp-cn/activity-feed\";\r\nimport { HttpRequest } from \"@eait-playerexp-cn/http\";\r\n\r\nexport default class ApplicationActivity {\r\n  static applicationError(error: Error | AxiosError, request: HttpRequest): Activity {\r\n    return Activity.error(\"application-error\", error.message || \"No message provided\", {\r\n      exception: error,\r\n      request\r\n    });\r\n  }\r\n}\r\n"}, "src/shared/logging/SentryRecorder.ts": {"language": "typescript", "mutants": [{"id": "src/shared/logging/SentryRecorder.ts@5:35-9:3\nBlockStatement: {}", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/logging/SentryRecorder.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 10}, "start": {"column": 36, "line": 6}}}, {"id": "src/shared/logging/SentryRecorder.ts@6:8-6:34\nConditionalExpression: true", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/logging/SentryRecorder.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 35, "line": 7}, "start": {"column": 9, "line": 7}}}, {"id": "src/shared/logging/SentryRecorder.ts@6:8-6:34\nConditionalExpression: false", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/logging/SentryRecorder.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 35, "line": 7}, "start": {"column": 9, "line": 7}}}, {"id": "src/shared/logging/SentryRecorder.ts@6:8-6:34\nEqualityOperator: activity.level !== \"ERROR\"", "mutatorName": "EqualityOperator", "replacement": "activity.level !== \"ERROR\"", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/logging/SentryRecorder.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 35, "line": 7}, "start": {"column": 9, "line": 7}}}, {"id": "src/shared/logging/SentryRecorder.ts@6:27-6:34\nStringLiteral: \"\"", "mutatorName": "StringLiteral", "replacement": "\"\"", "statusReason": "src/shared/logging/SentryRecorder.ts(7,9): error TS2367: This comparison appears to be unintentional because the types 'ActivityLevel' and '\"\"' have no overlap.\r\n", "status": "CompileError", "static": false, "relativeFileName": "src/shared/logging/SentryRecorder.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 35, "line": 7}, "start": {"column": 28, "line": 7}}}, {"id": "src/shared/logging/SentryRecorder.ts@6:36-8:5\nBlockStatement: {}", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/logging/SentryRecorder.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 6, "line": 9}, "start": {"column": 37, "line": 7}}}, {"id": "src/shared/logging/SentryRecorder.ts@7:58-7:95\nObjectLiteral: {}", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/logging/SentryRecorder.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 96, "line": 8}, "start": {"column": 59, "line": 8}}}], "source": "import { Activity, ActivityRecorder } from \"@eait-playerexp-cn/activity-feed\";\r\nimport * as Sentry from \"@sentry/nextjs\";\r\nimport { Extras } from \"@sentry/types\";\r\n\r\nexport default class SentryRecorder implements ActivityRecorder {\r\n  record(activity: Activity): void {\r\n    if (activity.level === \"ERROR\") {\r\n      Sentry.captureException(activity.context.exception, { extra: activity.context as Extras });\r\n    }\r\n  }\r\n}\r\n"}, "src/shared/logging/TelemetryRecorder.ts": {"language": "typescript", "mutants": [{"id": "src/shared/logging/TelemetryRecorder.ts@4:35-16:3\nBlockStatement: {}", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/logging/TelemetryRecorder.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 17}, "start": {"column": 36, "line": 5}}}, {"id": "src/shared/logging/TelemetryRecorder.ts@8:8-8:40\nBooleanLiteral: isSpanContextValid(spanContext)", "mutatorName": "<PERSON>olean<PERSON>iter<PERSON>", "replacement": "isSpanContextValid(spanContext)", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/logging/TelemetryRecorder.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 41, "line": 9}, "start": {"column": 9, "line": 9}}}, {"id": "src/shared/logging/TelemetryRecorder.ts@8:8-8:40\nConditionalExpression: true", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/logging/TelemetryRecorder.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 41, "line": 9}, "start": {"column": 9, "line": 9}}}, {"id": "src/shared/logging/TelemetryRecorder.ts@8:8-8:40\nConditionalExpression: false", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/logging/TelemetryRecorder.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 41, "line": 9}, "start": {"column": 9, "line": 9}}}, {"id": "src/shared/logging/TelemetryRecorder.ts@10:26-14:5\nObjectLiteral: {}", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/logging/TelemetryRecorder.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 6, "line": 15}, "start": {"column": 27, "line": 11}}}, {"id": "src/shared/logging/TelemetryRecorder.ts@15:23-15:64\nObjectLiteral: {}", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/logging/TelemetryRecorder.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 65, "line": 16}, "start": {"column": 24, "line": 16}}}], "source": "import { Activity, ActivityRecorder } from \"@eait-playerexp-cn/activity-feed\";\r\nimport { context, isSpanContextValid, trace } from \"@opentelemetry/api\";\r\n\r\nexport default class TelemetryRecorder implements ActivityRecorder {\r\n  record(activity: Activity): void {\r\n    const span = trace.getSpan(context.active());\r\n    const spanContext = span.spanContext();\r\n\r\n    if (!isSpanContextValid(spanContext)) return;\r\n\r\n    const tracingFields = {\r\n      traceId: spanContext.traceId,\r\n      correlationId: spanContext.traceId,\r\n      spanId: spanContext.spanId\r\n    };\r\n    activity.context = { ...activity.context, ...tracingFields };\r\n  }\r\n}\r\n"}, "src/shared/tokens/CachedAccessTokenProvider.ts": {"language": "typescript", "mutants": [{"id": "src/shared/tokens/CachedAccessTokenProvider.ts@4:38-4:51\nStringLiteral: \"\"", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/tokens/CachedAccessTokenProvider.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 52, "line": 5}, "start": {"column": 39, "line": 5}}}, {"id": "src/shared/tokens/CachedAccessTokenProvider.ts@11:44-20:3\nBlockStatement: {}", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/shared/tokens/CachedAccessTokenProvider.ts(14,24): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\r\n", "status": "CompileError", "static": false, "relativeFileName": "src/shared/tokens/CachedAccessTokenProvider.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 21}, "start": {"column": 45, "line": 12}}}, {"id": "src/shared/tokens/CachedAccessTokenProvider.ts@12:8-12:65\nConditionalExpression: true", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/tokens/CachedAccessTokenProvider.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 66, "line": 13}, "start": {"column": 9, "line": 13}}}, {"id": "src/shared/tokens/CachedAccessTokenProvider.ts@12:8-12:65\nConditionalExpression: false", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/tokens/CachedAccessTokenProvider.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 66, "line": 13}, "start": {"column": 9, "line": 13}}}, {"id": "src/shared/tokens/CachedAccessTokenProvider.ts@12:67-14:5\nBlockStatement: {}", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/tokens/CachedAccessTokenProvider.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 6, "line": 15}, "start": {"column": 68, "line": 13}}}, {"id": "src/shared/tokens/CachedAccessTokenProvider.ts@17:75-17:100\nArithmeticOperator: accessToken.expiresIn + 5", "mutatorName": "ArithmeticOperator", "replacement": "accessToken.expiresIn + 5", "status": "NoCoverage", "static": false, "relativeFileName": "src/shared/tokens/CachedAccessTokenProvider.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 101, "line": 18}, "start": {"column": 76, "line": 18}}}], "source": "import { AccessToken, OAuthTokenProvider, TokenProvider } from \"@eait-playerexp-cn/http-client\";\r\nimport { RedisCache } from \"@eait-playerexp-cn/server-kernel\";\r\n\r\nexport default class CachedAccessTokenProvider implements TokenProvider {\r\n  private static readonly CACHE_KEY = \"accessToken\";\r\n\r\n  constructor(\r\n    private readonly provider: OAuthTokenProvider,\r\n    private readonly cache: RedisCache\r\n  ) {}\r\n\r\n  async accessToken(): Promise<AccessToken> {\r\n    if (await this.cache.has(CachedAccessTokenProvider.CACHE_KEY)) {\r\n      return (await this.cache.get(CachedAccessTokenProvider.CACHE_KEY)) as AccessToken;\r\n    }\r\n\r\n    const accessToken = await this.provider.accessToken();\r\n    await this.cache.set(CachedAccessTokenProvider.CACHE_KEY, accessToken, accessToken.expiresIn - 5);\r\n\r\n    return accessToken;\r\n  }\r\n}\r\n"}, "src/middleware/OAuthErrorHandler.ts": {"language": "typescript", "mutants": [{"id": "src/middleware/OAuthErrorHandler.ts@16:6-16:42\nConditionalExpression: true", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "relativeFileName": "src/middleware/OAuthErrorHandler.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 43, "line": 17}, "start": {"column": 7, "line": 17}}}, {"id": "src/middleware/OAuthErrorHandler.ts@12:17-27:1\nBlockStatement: {}", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "relativeFileName": "src/middleware/OAuthErrorHandler.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 2, "line": 28}, "start": {"column": 18, "line": 13}}}, {"id": "src/middleware/OAuthErrorHandler.ts@16:6-16:42\nConditionalExpression: false", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "relativeFileName": "src/middleware/OAuthErrorHandler.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 43, "line": 17}, "start": {"column": 7, "line": 17}}}, {"id": "src/middleware/OAuthErrorHandler.ts@16:6-16:42\nOptionalChaining: (error as AxiosError).response.data", "mutatorName": "OptionalChaining", "replacement": "(error as AxiosError).response.data", "status": "NoCoverage", "static": false, "relativeFileName": "src/middleware/OAuthErrorHandler.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 43, "line": 17}, "start": {"column": 7, "line": 17}}}, {"id": "src/middleware/OAuthErrorHandler.ts@16:44-19:3\nBlockStatement: {}", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "relativeFileName": "src/middleware/OAuthErrorHandler.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 20}, "start": {"column": 45, "line": 17}}}, {"id": "src/middleware/OAuthErrorHandler.ts@18:24-18:60\nObjectLiteral: {}", "mutatorName": "ObjectLiteral", "replacement": "{}", "status": "NoCoverage", "static": false, "relativeFileName": "src/middleware/OAuthErrorHandler.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 61, "line": 19}, "start": {"column": 25, "line": 19}}}, {"id": "src/middleware/OAuthErrorHandler.ts@18:41-18:58\nConditionalExpression: true", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "relativeFileName": "src/middleware/OAuthErrorHandler.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 59, "line": 19}, "start": {"column": 42, "line": 19}}}, {"id": "src/middleware/OAuthErrorHandler.ts@18:41-18:58\nConditionalExpression: false", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "relativeFileName": "src/middleware/OAuthErrorHandler.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 59, "line": 19}, "start": {"column": 42, "line": 19}}}, {"id": "src/middleware/OAuthErrorHandler.ts@18:41-18:58\nLogicalOperator: message && detail", "mutatorName": "LogicalOperator", "replacement": "message && detail", "status": "NoCoverage", "static": false, "relativeFileName": "src/middleware/OAuthErrorHandler.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 59, "line": 19}, "start": {"column": 42, "line": 19}}}, {"id": "src/middleware/OAuthErrorHandler.ts@19:9-22:3\nBlockStatement: {}", "mutatorName": "BlockStatement", "replacement": "{}", "status": "NoCoverage", "static": false, "relativeFileName": "src/middleware/OAuthErrorHandler.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 4, "line": 23}, "start": {"column": 10, "line": 20}}}, {"id": "src/middleware/OAuthErrorHandler.ts@21:24-21:62\nConditionalExpression: true", "mutatorName": "ConditionalExpression", "replacement": "true", "status": "NoCoverage", "static": false, "relativeFileName": "src/middleware/OAuthErrorHandler.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 63, "line": 22}, "start": {"column": 25, "line": 22}}}, {"id": "src/middleware/OAuthErrorHandler.ts@21:24-21:62\nConditionalExpression: false", "mutatorName": "ConditionalExpression", "replacement": "false", "status": "NoCoverage", "static": false, "relativeFileName": "src/middleware/OAuthErrorHandler.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 63, "line": 22}, "start": {"column": 25, "line": 22}}}, {"id": "src/middleware/OAuthErrorHandler.ts@21:24-21:62\nLogicalOperator: error.message && \"No message provided\"", "mutatorName": "LogicalOperator", "replacement": "error.message && \"No message provided\"", "status": "NoCoverage", "static": false, "relativeFileName": "src/middleware/OAuthErrorHandler.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 63, "line": 22}, "start": {"column": 25, "line": 22}}}, {"id": "src/middleware/OAuthErrorHandler.ts@21:41-21:62\nStringLiteral: \"\"", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "relativeFileName": "src/middleware/OAuthErrorHandler.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 63, "line": 22}, "start": {"column": 42, "line": 22}}}, {"id": "src/middleware/OAuthErrorHandler.ts@26:10-26:44\nStringLiteral: \"\"", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "relativeFileName": "src/middleware/OAuthErrorHandler.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 45, "line": 27}, "start": {"column": 11, "line": 27}}}], "source": "import { NextApiResponse } from \"next\";\r\nimport ApplicationActivity from \"@src/shared/logging/ApplicationActivity\";\r\nimport { AxiosError } from \"axios\";\r\nimport { ApiProblem } from \"@eait-playerexp-cn/api-problem\";\r\nimport ApiContainer from \"@src/ApiContainer\";\r\nimport { ActivityFeed } from \"@eait-playerexp-cn/activity-feed\";\r\nimport { NextApiRequestWithMultipartFile, RequestFactory } from \"@eait-playerexp-cn/server-kernel\";\r\n\r\nasync function onOAuthError(\r\n  error: Error | AxiosError,\r\n  req: NextApiRequestWithMultipartFile,\r\n  res: NextApiResponse\r\n): Promise<void> {\r\n  const requestFactory = new RequestFactory();\r\n  const feed = ApiContainer.get(ActivityFeed);\r\n\r\n  if ((error as AxiosError).response?.data) {\r\n    const { code, message, detail } = (error as AxiosError<ApiProblem>).response.data;\r\n    req.session.error = { code, message: message || detail }; // Error from Java API\r\n  } else {\r\n    feed.add(ApplicationActivity.applicationError(error, requestFactory.createRequestFrom(req)));\r\n    req.session.error = error.message || \"No message provided\"; // Error message from API handler\r\n  }\r\n\r\n  req.session.save();\r\n\r\n  res.end(\"<script>window.close();</script>\");\r\n}\r\n\r\nexport default onOAuthError;\r\n"}, "src/middleware/Session.ts": {"language": "typescript", "mutants": [{"id": "src/middleware/Session.ts@4:48-4:64\nStringLiteral: \"\"", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "relativeFileName": "src/middleware/Session.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 65, "line": 5}, "start": {"column": 49, "line": 5}}}], "source": "import \"reflect-metadata\";\r\nimport { sessionFactory } from \"@eait-playerexp-cn/server-kernel\";\r\nimport ApiContainer from \"@src/ApiContainer\";\r\n\r\nconst session = sessionFactory(ApiContainer.get(\"sessionOptions\"));\r\n\r\nexport default session;\r\n"}, "src/middleware/WithCors.ts": {"language": "typescript", "mutants": [{"id": "src/middleware/WithCors.ts@3:50-3:69\nStringLiteral: \"\"", "mutatorName": "StringLiteral", "replacement": "\"\"", "status": "NoCoverage", "static": false, "relativeFileName": "src/middleware/WithCors.ts", "killedBy": [], "coveredBy": [], "location": {"end": {"column": 70, "line": 4}, "start": {"column": 51, "line": 4}}}], "source": "import { withCorsFactory } from \"@eait-playerexp-cn/server-kernel\";\r\nimport ApiContainer from \"@src/ApiContainer\";\r\n\r\nconst withCors = withCorsFactory(ApiContainer.get(\"corsConfiguration\"));\r\n\r\nexport default withCors;\r\n"}, "src/server/channels/facebook/FacebookPageCredentials.ts": {"language": "typescript", "mutants": [{"id": "16", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/channels/facebook/FacebookPageCredentials.ts(2,82): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\r\n", "status": "CompileError", "static": false, "coveredBy": ["1", "4"], "location": {"end": {"column": 4, "line": 4}, "start": {"column": 106, "line": 2}}}, {"id": "17", "mutatorName": "BlockStatement", "replacement": "{}", "statusReason": "src/server/channels/facebook/FacebookPageCredentials.ts(6,92): error TS2355: A function whose declared type is neither 'undefined', 'void', nor 'any' must return a value.\r\n", "status": "CompileError", "static": false, "coveredBy": ["0", "2", "3"], "location": {"end": {"column": 4, "line": 8}, "start": {"column": 116, "line": 6}}}], "source": "export default class FacebookPageCredentials {\r\n  static forCreator(pageId: string, pageAccessToken: string, creatorId: string): FacebookPageCredentials {\r\n    return new FacebookPageCredentials(pageId, pageAccessToken, creatorId);\r\n  }\r\n\r\n  static forInterestedCreator(pageId: string, pageAccessToken: string, nucleusId: number): FacebookPageCredentials {\r\n    return new FacebookPageCredentials(pageId, pageAccessToken, null, nucleusId);\r\n  }\r\n\r\n  private constructor(\r\n    readonly pageId: string,\r\n    readonly pageAccessToken: string,\r\n    readonly creatorId: string,\r\n    readonly nucleusId?: number\r\n  ) {}\r\n}\r\n"}}, "schemaVersion": "1.0", "thresholds": {"high": 90, "low": 70, "break": 54}, "testFiles": {"__tests__/src/server/channels/facebook/ConnectFacebookPageController.spec.ts": {"tests": [{"id": "0", "name": "ConnectFacebookPageController connects a Facebook page for an interested creator with string nucleusId", "location": {"start": {"column": 4, "line": 34}}}, {"id": "1", "name": "ConnectFacebookPageController connects a Facebook page for a creator", "location": {"start": {"column": 4, "line": 58}}}, {"id": "2", "name": "ConnectFacebookPageController connects a Facebook page for an interested creator with number nucleusId", "location": {"start": {"column": 4, "line": 82}}}, {"id": "3", "name": "ConnectFacebookPageController connects a Facebook page for an interested creator using identity.type with FLAG_PER_PROGRAM_PROFILE enabled", "location": {"start": {"column": 4, "line": 106}}}, {"id": "4", "name": "ConnectFacebookPageController connects a Facebook page for a creator using identity.type with FLAG_PER_PROGRAM_PROFILE enabled", "location": {"start": {"column": 4, "line": 144}}}], "source": "import \"reflect-metadata\";\r\nimport { createMocks, MockResponse } from \"node-mocks-http\";\r\nimport { NextApiResponse } from \"next\";\r\nimport { NextApiRequestWithSession } from \"@eait-playerexp-cn/server-kernel\";\r\nimport ConnectFacebookPageController from \"@src/server/channels/facebook/ConnectFacebookPageController\";\r\nimport ConnectedAccountsHttpClient from \"@src/server/connecteAccounts/ConnectedAccountsHttpClient\";\r\nimport { Random } from \"@eait-playerexp-cn/interested-creators-ui\";\r\n\r\n// Reference to the mocked config (already mocked in jest.setup.ts)\r\nconst mockConfig = jest.requireMock(\"../../../../../config\");\r\n\r\ndescribe(\"ConnectFacebookPageController\", () => {\r\n  let controller: ConnectFacebookPageController;\r\n  let connectedAccounts: ConnectedAccountsHttpClient;\r\n  const session = { save: jest.fn() };\r\n  // Store original identity method\r\n  const originalIdentityMethod = ConnectFacebookPageController.prototype.identity;\r\n\r\n  beforeEach(() => {\r\n    jest.clearAllMocks();\r\n    connectedAccounts = { connectFacebookPage: jest.fn() } as unknown as ConnectedAccountsHttpClient;\r\n    controller = new ConnectFacebookPageController(connectedAccounts);\r\n    // Reset FLAG_PER_PROGRAM_PROFILE to false before each test\r\n    mockConfig.FLAG_PER_PROGRAM_PROFILE = false;\r\n  });\r\n\r\n  afterEach(() => {\r\n    // Restore original identity method after each test\r\n    ConnectFacebookPageController.prototype.identity = originalIdentityMethod;\r\n    // Reset FLAG_PER_PROGRAM_PROFILE to false after each test\r\n    mockConfig.FLAG_PER_PROGRAM_PROFILE = false;\r\n  });\r\n\r\n  it(\"connects a Facebook page for an interested creator with string nucleusId\", async () => {\r\n    const credentials = {\r\n      nucleusId: \"*********\", // String nucleusId\r\n      pageAccessToken: Random.string(),\r\n      pageId: \"a057717c-01f2-49de-9422-289b06649809\",\r\n      creatorId: null\r\n    };\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"POST\",\r\n      url: \"/api/facebook-channels\",\r\n      body: credentials,\r\n      session: { ...session, nucleusId: credentials.nucleusId }\r\n    });\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(req.session.fbPages).toBeUndefined();\r\n    expect(req.session.accountType).toEqual(\"Facebook\");\r\n    expect(req.session.save).toHaveBeenCalledTimes(2);\r\n    expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledTimes(1);\r\n    expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledWith(credentials);\r\n  });\r\n\r\n  it(\"connects a Facebook page for a creator\", async () => {\r\n    const credentials = {\r\n      creatorId: \"15d7210f-0882-4b33-87e9-dcd5fea75e3d\",\r\n      pageAccessToken: Random.string(),\r\n      pageId: \"a057717c-01f2-49de-9422-289b06649809\",\r\n      nucleusId: undefined\r\n    };\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"POST\",\r\n      url: \"/api/facebook-channels\",\r\n      body: credentials,\r\n      session: { ...session, user: { id: credentials.creatorId } }\r\n    });\r\n\r\n    await controller.handle(req as unknown as NextApiRequestWithSession, res as unknown as NextApiResponse);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(req.session.fbPages).toBeUndefined();\r\n    expect(req.session.accountType).toEqual(\"Facebook\");\r\n    expect(req.session.save).toHaveBeenCalledTimes(2);\r\n    expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledTimes(1);\r\n    expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledWith(credentials);\r\n  });\r\n\r\n  it(\"connects a Facebook page for an interested creator with number nucleusId\", async () => {\r\n    const credentials = {\r\n      nucleusId: *********, // Number nucleusId\r\n      pageAccessToken: Random.string(),\r\n      pageId: \"a057717c-01f2-49de-9422-289b06649809\",\r\n      creatorId: null\r\n    };\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"POST\",\r\n      url: \"/api/facebook-channels\",\r\n      body: credentials,\r\n      session: { ...session, nucleusId: credentials.nucleusId }\r\n    });\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(req.session.fbPages).toBeUndefined();\r\n    expect(req.session.accountType).toEqual(\"Facebook\");\r\n    expect(req.session.save).toHaveBeenCalledTimes(2);\r\n    expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledTimes(1);\r\n    expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledWith(credentials);\r\n  });\r\n\r\n  it(\"connects a Facebook page for an interested creator using identity.type with FLAG_PER_PROGRAM_PROFILE enabled\", async () => {\r\n    // Enable the flag for this test\r\n    mockConfig.FLAG_PER_PROGRAM_PROFILE = true;\r\n\r\n    // Mock the identity method\r\n    const nucleusId = *********;\r\n    ConnectFacebookPageController.prototype.identity = jest.fn().mockReturnValue({\r\n      type: \"INTERESTED_CREATOR\",\r\n      nucleusId\r\n    });\r\n\r\n    // Create a new controller instance\r\n    const controller = new ConnectFacebookPageController(connectedAccounts);\r\n\r\n    const credentials = {\r\n      nucleusId,\r\n      pageAccessToken: Random.string(),\r\n      pageId: \"a057717c-01f2-49de-9422-289b06649809\",\r\n      creatorId: null\r\n    };\r\n\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"POST\",\r\n      url: \"/api/facebook-channels\",\r\n      body: { pageId: credentials.pageId, pageAccessToken: credentials.pageAccessToken },\r\n      session\r\n    });\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(req.session.fbPages).toBeUndefined();\r\n    expect(req.session.accountType).toEqual(\"Facebook\");\r\n    expect(req.session.save).toHaveBeenCalledTimes(2);\r\n    expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledTimes(1);\r\n    expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledWith(credentials);\r\n  });\r\n\r\n  it(\"connects a Facebook page for a creator using identity.type with FLAG_PER_PROGRAM_PROFILE enabled\", async () => {\r\n    // Enable the flag for this test\r\n    mockConfig.FLAG_PER_PROGRAM_PROFILE = true;\r\n\r\n    // Mock the identity method\r\n    const creatorId = \"creator-id-12345\";\r\n    ConnectFacebookPageController.prototype.identity = jest.fn().mockReturnValue({\r\n      type: \"CREATOR\", // Not an INTERESTED_CREATOR\r\n      id: creatorId\r\n    });\r\n\r\n    // Create a new controller instance\r\n    const controller = new ConnectFacebookPageController(connectedAccounts);\r\n\r\n    const credentials = {\r\n      creatorId,\r\n      pageAccessToken: Random.string(),\r\n      pageId: \"a057717c-01f2-49de-9422-289b06649809\",\r\n      nucleusId: undefined\r\n    };\r\n\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"POST\",\r\n      url: \"/api/facebook-channels\",\r\n      body: { pageId: credentials.pageId, pageAccessToken: credentials.pageAccessToken },\r\n      session\r\n    });\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(req.session.fbPages).toBeUndefined();\r\n    expect(req.session.accountType).toEqual(\"Facebook\");\r\n    expect(req.session.save).toHaveBeenCalledTimes(2);\r\n    expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledTimes(1);\r\n    expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledWith(credentials);\r\n  });\r\n});\r\n"}, "__tests__/src/server/connecteAccounts/ConnectedAccountsHttpClient.spec.ts": {"tests": [{"id": "5", "name": "ConnectedAccountsHttpClient finds all connected accounts", "location": {"start": {"column": 4, "line": 7}}}, {"id": "6", "name": "ConnectedAccountsHttpClient finds all connected accounts with expiration status using number nucleusId", "location": {"start": {"column": 4, "line": 31}}}, {"id": "7", "name": "ConnectedAccountsHttpClient finds all connected accounts with expiration status using string nucleusId", "location": {"start": {"column": 4, "line": 55}}}, {"id": "8", "name": "ConnectedAccountsHttpClient connects a Facebook page", "location": {"start": {"column": 4, "line": 80}}}, {"id": "9", "name": "ConnectedAccountsHttpClient removes a connected account for an interested creator", "location": {"start": {"column": 4, "line": 98}}}], "source": "import \"reflect-metadata\";\r\nimport { TraceableHttpClient } from \"@eait-playerexp-cn/http-client\";\r\nimport { Random } from \"@eait-playerexp-cn/interested-creators-ui\";\r\nimport ConnectedAccountsHttpClient from \"@src/server/connecteAccounts/ConnectedAccountsHttpClient\";\r\n\r\ndescribe(\"ConnectedAccountsHttpClient\", () => {\r\n  it(\"finds all connected accounts\", async () => {\r\n    const account = {\r\n      name: Random.firstName(),\r\n      disconnected: false,\r\n      username: Random.string(),\r\n      id: Random.uuid(),\r\n      type: \"YOUTUBE\",\r\n      uri: Random.url(),\r\n      thumbnail: Random.imageUrl(),\r\n      isExpired: true,\r\n      accountId: Random.uuid()\r\n    };\r\n    const connectedAccounts = [account];\r\n    const nucleusId = 123456;\r\n    const client = { get: jest.fn().mockReturnValue({ data: connectedAccounts }) };\r\n    const connectedAccountsHttpClient = new ConnectedAccountsHttpClient(client as unknown as TraceableHttpClient);\r\n\r\n    const accounts = await connectedAccountsHttpClient.getAllConnectedAccounts(nucleusId);\r\n\r\n    expect(accounts).toEqual(connectedAccounts);\r\n    expect(client.get).toHaveBeenCalledTimes(1);\r\n    expect(client.get).toHaveBeenCalledWith(`/v1/connected-accounts/${nucleusId}`);\r\n  });\r\n\r\n  it(\"finds all connected accounts with expiration status using number nucleusId\", async () => {\r\n    const account = {\r\n      name: Random.firstName(),\r\n      disconnected: false,\r\n      username: Random.string(),\r\n      id: Random.uuid(),\r\n      type: \"YOUTUBE\",\r\n      uri: Random.url(),\r\n      thumbnail: Random.imageUrl(),\r\n      isExpired: true,\r\n      accountId: Random.uuid()\r\n    };\r\n    const connectedAccounts = [account];\r\n    const nucleusId = 123456; // Number nucleusId\r\n    const client = { get: jest.fn().mockReturnValue({ data: connectedAccounts }) };\r\n    const connectedAccountsHttpClient = new ConnectedAccountsHttpClient(client as unknown as TraceableHttpClient);\r\n\r\n    const accounts = await connectedAccountsHttpClient.getAllConnectedAccountsWithExpirationStatus(nucleusId);\r\n\r\n    expect(accounts).toEqual(connectedAccounts);\r\n    expect(client.get).toHaveBeenCalledTimes(1);\r\n    expect(client.get).toHaveBeenCalledWith(`/v2/connected-accounts/${nucleusId}`);\r\n  });\r\n\r\n  it(\"finds all connected accounts with expiration status using string nucleusId\", async () => {\r\n    const account = {\r\n      name: Random.firstName(),\r\n      disconnected: false,\r\n      username: Random.string(),\r\n      id: Random.uuid(),\r\n      type: \"YOUTUBE\",\r\n      uri: Random.url(),\r\n      thumbnail: Random.imageUrl(),\r\n      isExpired: true,\r\n      accountId: Random.uuid()\r\n    };\r\n    const connectedAccounts = [account];\r\n    const nucleusId = \"123456\"; // String nucleusId\r\n    const client = { get: jest.fn().mockReturnValue({ data: connectedAccounts }) };\r\n    const connectedAccountsHttpClient = new ConnectedAccountsHttpClient(client as unknown as TraceableHttpClient);\r\n\r\n    // TypeScript allows this because of type coercion at runtime\r\n    const accounts = await connectedAccountsHttpClient.getAllConnectedAccountsWithExpirationStatus(Number(nucleusId));\r\n\r\n    expect(accounts).toEqual(connectedAccounts);\r\n    expect(client.get).toHaveBeenCalledTimes(1);\r\n    expect(client.get).toHaveBeenCalledWith(`/v2/connected-accounts/${nucleusId}`);\r\n  });\r\n\r\n  it(\"connects a Facebook page\", async () => {\r\n    const nucleusId = 123456;\r\n    const credentials = {\r\n      accessToken: Random.string(),\r\n      creatorId: null,\r\n      nucleusId,\r\n      pageAccessToken: Random.string(),\r\n      pageId: \"a057717c-01f2-49de-9422-289b06649809\"\r\n    };\r\n    const client = { post: jest.fn().mockReturnValue({ data: null }) };\r\n    const connectedAccountsHttpClient = new ConnectedAccountsHttpClient(client as unknown as TraceableHttpClient);\r\n\r\n    await connectedAccountsHttpClient.connectFacebookPage(credentials);\r\n\r\n    expect(client.post).toHaveBeenCalledTimes(1);\r\n    expect(client.post).toHaveBeenCalledWith(\"/v1/facebook-accounts\", { body: credentials });\r\n  });\r\n\r\n  it(\"removes a connected account for an interested creator\", async () => {\r\n    const accountId = \"a1LDF00000K4z0i2AB\";\r\n    const type = \"INTERESTED_CREATOR\";\r\n    const client = { delete: jest.fn().mockReturnValue({ data: null }) };\r\n    const connectedAccountsHttpClient = new ConnectedAccountsHttpClient(client as unknown as TraceableHttpClient);\r\n\r\n    await connectedAccountsHttpClient.removeConnectedAccount(accountId, type);\r\n\r\n    expect(client.delete).toHaveBeenCalledTimes(1);\r\n    expect(client.delete).toHaveBeenCalledWith(`/v1/connected-accounts/${accountId}`, { query: { type } });\r\n  });\r\n});\r\n"}, "__tests__/src/server/contentScanning/ContentScanningHttpClient.spec.ts": {"tests": [{"id": "10", "name": "ContentScanningHttpClient throws error if it's an unauthorized user", "location": {"start": {"column": 4, "line": 10}}}, {"id": "11", "name": "ContentScanningHttpClient throws error if it's an invalid input", "location": {"start": {"column": 4, "line": 28}}}, {"id": "12", "name": "ContentScanningHttpClient throws error if HTTP request fails", "location": {"start": {"column": 4, "line": 52}}}, {"id": "13", "name": "ContentScanningHttpClient urls verified successfully", "location": {"start": {"column": 4, "line": 81}}}], "source": "import \"reflect-metadata\";\r\nimport { type TraceableHttpClient } from \"@eait-playerexp-cn/http-client\";\r\nimport ContentScanningHttpClient from \"@src/server/contentScanning/ContentScanningHttpClient\";\r\nimport { Random } from \"@eait-playerexp-cn/interested-creators-ui\";\r\nimport { HttpStatus } from \"@eait-playerexp-cn/http\";\r\n\r\ndescribe(\"ContentScanningHttpClient\", () => {\r\n  beforeEach(() => jest.clearAllMocks());\r\n\r\n  it(\"throws error if it's an unauthorized user\", async () => {\r\n    const existingApplication = {\r\n      error: \"unauthorized\",\r\n      error_description: \"An Authentication object was not found in the SecurityContext\"\r\n    };\r\n    const client = { post: jest.fn().mockReturnValue({ data: existingApplication }) };\r\n    const urls = [Random.url(), Random.url()];\r\n    const applications = new ContentScanningHttpClient(client as unknown as TraceableHttpClient);\r\n\r\n    const application = await applications.verifyUrls({ urls }, \"INTERESTED_CREATORS\");\r\n\r\n    expect(application).toEqual(existingApplication);\r\n    expect(client.post).toHaveBeenCalledTimes(1);\r\n    expect(client.post).toHaveBeenCalledWith(\"/v1/secure-content?type=INTERESTED_CREATORS\", {\r\n      body: { urls: { urls } }\r\n    });\r\n  });\r\n\r\n  it(\"throws error if it's an invalid input\", async () => {\r\n    const existingApplication = {\r\n      code: \"validate-content-urls-invalid-input\",\r\n      message: \"Cannot validate content urls, invalid input provided\",\r\n      title: \"Unprocessable Entity\",\r\n      type: \"https://tools.ietf.org//html//rfc4918#section-11.2\",\r\n      status: HttpStatus.UNPROCESSABLE_ENTITY_CODE,\r\n      errors: {\r\n        \"urls[1]\": \"URL is invalid\"\r\n      }\r\n    };\r\n    const client = { post: jest.fn().mockReturnValue({ data: existingApplication }) };\r\n    const urls = [\"http://reddit.com\", \"http://google.com\"];\r\n    const applications = new ContentScanningHttpClient(client as unknown as TraceableHttpClient);\r\n\r\n    const application = await applications.verifyUrls({ urls }, \"INTERESTED_CREATORS\");\r\n\r\n    expect(application).toEqual(existingApplication);\r\n    expect(client.post).toHaveBeenCalledTimes(1);\r\n    expect(client.post).toHaveBeenCalledWith(\"/v1/secure-content?type=INTERESTED_CREATORS\", {\r\n      body: { urls: { urls } }\r\n    });\r\n  });\r\n\r\n  it(\"throws error if HTTP request fails\", async () => {\r\n    const existingApplication = {\r\n      status: HttpStatus.INTERNAL_SERVER_ERROR_CODE,\r\n      code: \"view-aggregate-error\",\r\n      message: \"Cannot view aggregate because: {exception.message}\",\r\n      exception: {\r\n        message: \"Illegal base64 character 25\",\r\n        class: \"com.eaitplayerexp.somepackage.SomeClass\",\r\n        file: \"SomeClass.class\",\r\n        line: 298,\r\n        stackTrace: [],\r\n        previous: \"string\"\r\n      },\r\n      title: \"Internal Server Error\",\r\n      type: \"https://www.w3.org/Protocols/rfc2616/rfc2616-sec10.html#sec10.5.1\"\r\n    };\r\n    const client = { post: jest.fn().mockReturnValue({ data: existingApplication }) };\r\n    const urls = [\"http://reddit.com\", \"http://google.com\"];\r\n    const applications = new ContentScanningHttpClient(client as unknown as TraceableHttpClient);\r\n\r\n    const application = await applications.verifyUrls({ urls }, \"INTERESTED_CREATORS\");\r\n\r\n    expect(application).toEqual(existingApplication);\r\n    expect(client.post).toHaveBeenCalledTimes(1);\r\n    expect(client.post).toHaveBeenCalledWith(\"/v1/secure-content?type=INTERESTED_CREATORS\", {\r\n      body: { urls: { urls } }\r\n    });\r\n  });\r\n\r\n  it(\"urls verified successfully\", async () => {\r\n    const existingApplication = {\r\n      results: [\r\n        {\r\n          url: \"https://www.reddit.com\",\r\n          isSecure: true\r\n        }\r\n      ]\r\n    };\r\n    const client = { post: jest.fn().mockReturnValue({ data: existingApplication }) };\r\n    const urls = [Random.url()];\r\n    const applications = new ContentScanningHttpClient(client as unknown as TraceableHttpClient);\r\n\r\n    const application = await applications.verifyUrls({ urls }, \"INTERESTED_CREATORS\");\r\n\r\n    expect(application).toEqual(existingApplication);\r\n    expect(client.post).toHaveBeenCalledTimes(1);\r\n    expect(client.post).toHaveBeenCalledWith(\"/v1/secure-content?type=INTERESTED_CREATORS\", {\r\n      body: { urls: { urls } }\r\n    });\r\n  });\r\n});\r\n"}, "__tests__/src/server/interestedCreator/InterestedCreator.spec.ts": {"tests": [{"id": "14", "name": "InterestedCreator formats the content urls and its followers", "location": {"start": {"column": 4, "line": 47}}}, {"id": "15", "name": "InterestedCreator formats the content urls as additional links", "location": {"start": {"column": 4, "line": 67}}}, {"id": "16", "name": "InterestedCreator it removes empty or invalid URLs", "location": {"start": {"column": 4, "line": 84}}}], "source": "import InterestedCreator from \"@src/server/interestedCreator/InterestedCreator\";\r\nimport {\r\n  Random,\r\n  aSecondaryFranchise,\r\n  aPrimaryFranchise,\r\n  anInterestedCreator\r\n} from \"@eait-playerexp-cn/interested-creators-ui\";\r\nimport { aCountry, aCreatorType, aLanguage } from \"@eait-playerexp-cn/metadata-test-fixtures\";\r\nimport { Country, CountryResponse } from \"@eait-playerexp-cn/metadata-types\";\r\n\r\ndescribe(\"InterestedCreator\", () => {\r\n  const creatorTypes = [aCreatorType(), aCreatorType()];\r\n  const countryCode = \"US\";\r\n  const creatorCountry = Country.fromApi(aCountry({ code: countryCode }) as unknown as CountryResponse);\r\n  const updatedPreferredEmail = Random.email();\r\n  const preferredFranchises = [aPrimaryFranchise(), aSecondaryFranchise()];\r\n  const interestedCreator = anInterestedCreator({\r\n    country: creatorCountry,\r\n    contentLanguages: [aLanguage({ value: \"en\" })],\r\n    preferredFranchises: preferredFranchises,\r\n    preferredLanguage: aCountry({ code: \"en_US\", name: \"English\" }),\r\n    creatorTypes,\r\n    preferredEmail: updatedPreferredEmail\r\n  });\r\n  const creatorProgram = {\r\n    code: \"affiliate\"\r\n  };\r\n  const url = Random.url();\r\n  const expectedCompletedApplication = {\r\n    defaultGamerTag: interestedCreator.defaultGamerTag,\r\n    nucleusId: interestedCreator.nucleusId,\r\n    firstName: interestedCreator.firstName,\r\n    lastName: interestedCreator.lastName,\r\n    originEmail: interestedCreator.originEmail,\r\n    dateOfBirth: interestedCreator.dateOfBirth,\r\n    preferredEmail: interestedCreator.preferredEmail,\r\n    countryCode,\r\n    creatorTypes: [\r\n      (interestedCreator.creatorTypes[0] as unknown as { value: string }).value,\r\n      (interestedCreator.creatorTypes[1] as unknown as { value: string })?.value\r\n    ],\r\n    contentLanguages: [interestedCreator.contentLanguages[0].value],\r\n    preferredFranchises: interestedCreator.preferredFranchises,\r\n    preferredLanguage: interestedCreator.preferredLanguage\r\n  };\r\n\r\n  it(\"formats the content urls and its followers\", async () => {\r\n    const interestedCreatorWithContentUrls = new InterestedCreator({\r\n      ...interestedCreator,\r\n      contentUrls: [{ url: url, followers: Random.number(1, 100) }]\r\n    } as unknown as InterestedCreator);\r\n    const expectedCompletedApplicationWithContentUrls = {\r\n      ...expectedCompletedApplication,\r\n      contentAccounts: [\r\n        {\r\n          uri: interestedCreatorWithContentUrls.contentUrls[0].url,\r\n          followersCount: interestedCreatorWithContentUrls.contentUrls[0].followers\r\n        }\r\n      ]\r\n    };\r\n\r\n    const completedApplication = interestedCreatorWithContentUrls.complete();\r\n\r\n    expect(completedApplication).toEqual(expectedCompletedApplicationWithContentUrls);\r\n  });\r\n\r\n  it(\"formats the content urls as additional links\", async () => {\r\n    const interestedCreatorWithAdditionalLinks = new InterestedCreator({\r\n      ...interestedCreator,\r\n      contentUrls: [{ url: url, followers: 0 }],\r\n      creatorProgram\r\n    } as unknown as InterestedCreator);\r\n    const expectedCompletedApplicationWithAdditionalLinks = {\r\n      ...expectedCompletedApplication,\r\n      additionalLinks: [url],\r\n      creatorProgram\r\n    };\r\n\r\n    const completedApplication = interestedCreatorWithAdditionalLinks.completeWithAdditionalLinks();\r\n\r\n    expect(completedApplication).toEqual(expectedCompletedApplicationWithAdditionalLinks);\r\n  });\r\n\r\n  it(\"it removes empty or invalid URLs\", async () => {\r\n    const interestedCreatorWithAdditionalLinks = new InterestedCreator({\r\n      ...interestedCreator,\r\n      contentUrls: [{ url: \"https://\", followers: 0 }],\r\n      creatorProgram\r\n    } as unknown as InterestedCreator);\r\n    const expectedCompletedApplicationWithAdditionalLinks = {\r\n      ...expectedCompletedApplication,\r\n      creatorProgram,\r\n      additionalLinks: []\r\n    };\r\n\r\n    const completedApplication = interestedCreatorWithAdditionalLinks.completeWithAdditionalLinks();\r\n\r\n    expect(completedApplication).toEqual(expectedCompletedApplicationWithAdditionalLinks);\r\n  });\r\n});\r\n"}, "__tests__/src/server/connecteAccounts/ViewConnectedAccountsController.spec.ts": {"tests": [{"id": "17", "name": "ViewConnectedAccountsController fetch connected accounts for the interested creators", "location": {"start": {"column": 4, "line": 27}}}, {"id": "18", "name": "ViewConnectedAccountsController finds all connected accounts for an interested creator with expiration status", "location": {"start": {"column": 4, "line": 50}}}], "source": "import \"reflect-metadata\";\r\nimport { createMocks, MockResponse } from \"node-mocks-http\";\r\nimport { NextApiRequestWithSession } from \"@eait-playerexp-cn/server-kernel\";\r\nimport { NextApiResponse } from \"next\";\r\nimport ViewConnectedAccountsController from \"@src/server/connecteAccounts/ViewConnectedAccountsController\";\r\nimport config from \"config\";\r\nimport ConnectedAccountsHttpClient from \"@src/server/connecteAccounts/ConnectedAccountsHttpClient\";\r\nimport { Random } from \"@eait-playerexp-cn/interested-creators-ui\";\r\n\r\ndescribe(\"ViewConnectedAccountsController\", () => {\r\n  let controller: ViewConnectedAccountsController;\r\n  const session = { save: jest.fn() };\r\n  const account = {\r\n    name: Random.firstName(),\r\n    disconnected: false,\r\n    username: Random.string(),\r\n    id: Random.uuid(),\r\n    type: \"YOUTUBE\",\r\n    uri: Random.url(),\r\n    thumbnail: Random.imageUrl(),\r\n    isExpired: true,\r\n    accountId: Random.uuid()\r\n  };\r\n\r\n  beforeEach(() => jest.clearAllMocks());\r\n\r\n  it(\"fetch connected accounts for the interested creators\", async () => {\r\n    config.INTERESTED_CREATOR_REAPPLY_PERIOD = false;\r\n    const nucleusId = **********;\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"GET\",\r\n      url: \"/api/connected-accounts\",\r\n      query: { nucleusId },\r\n      session\r\n    });\r\n    const connectedAccounts = [account];\r\n    const accounts = { getAllConnectedAccounts: jest.fn().mockResolvedValue(connectedAccounts) };\r\n    controller = new ViewConnectedAccountsController(accounts as unknown as ConnectedAccountsHttpClient);\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(JSON.parse(res._getData())).toEqual(connectedAccounts);\r\n    expect(accounts.getAllConnectedAccounts).toHaveBeenCalledTimes(1);\r\n    expect(accounts.getAllConnectedAccounts).toHaveBeenCalledWith(nucleusId);\r\n    expect(req.session.nucleusId).toEqual(nucleusId);\r\n    expect(req.session.save).toHaveBeenCalledTimes(1);\r\n  });\r\n\r\n  it(\"finds all connected accounts for an interested creator with expiration status\", async () => {\r\n    config.INTERESTED_CREATOR_REAPPLY_PERIOD = true;\r\n    const nucleusId = **********;\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"GET\",\r\n      url: \"/api/v2/connected-accounts\",\r\n      query: { nucleusId },\r\n      session\r\n    });\r\n    const connectedAccounts = [account];\r\n    const accounts = { getAllConnectedAccountsWithExpirationStatus: jest.fn().mockResolvedValue(connectedAccounts) };\r\n    controller = new ViewConnectedAccountsController(accounts as unknown as ConnectedAccountsHttpClient);\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(JSON.parse(res._getData())).toEqual(connectedAccounts);\r\n    expect(accounts.getAllConnectedAccountsWithExpirationStatus).toHaveBeenCalledTimes(1);\r\n    expect(accounts.getAllConnectedAccountsWithExpirationStatus).toHaveBeenCalledWith(nucleusId);\r\n    expect(req.session.nucleusId).toEqual(nucleusId);\r\n    expect(req.session.save).toHaveBeenCalledTimes(1);\r\n  });\r\n});\r\n"}, "__tests__/src/server/interestedCreator/InterestedCreatorsHttpClient.spec.ts": {"tests": [{"id": "19", "name": "InterestedCreatorsHttpClient submits an interested creator information", "location": {"start": {"column": 4, "line": 52}}}], "source": "import \"reflect-metadata\";\r\nimport {\r\n  Random,\r\n  aSecondaryFranchise,\r\n  aPrimaryFranchise,\r\n  anInterestedCreator\r\n} from \"@eait-playerexp-cn/interested-creators-ui\";\r\nimport { aCountry, aCreatorType, aLanguage } from \"@eait-playerexp-cn/metadata-test-fixtures\";\r\nimport InterestedCreatorsHttpClient from \"@src/server/interestedCreator/InterestedCreatorsHttpClient\";\r\nimport { TraceableHttpClient } from \"@eait-playerexp-cn/http-client\";\r\nimport { Country, CountryResponse } from \"@eait-playerexp-cn/metadata-types\";\r\nimport InterestedCreator from \"@src/server/interestedCreator/InterestedCreator\";\r\n\r\ndescribe(\"InterestedCreatorsHttpClient\", () => {\r\n  const creatorTypes = [aCreatorType(), aCreatorType()];\r\n  const countryCode = \"US\";\r\n  const creatorCountry = Country.fromApi(aCountry({ code: countryCode }) as unknown as CountryResponse);\r\n  const updatedPreferredEmail = Random.email();\r\n  const preferredFranchises = [aPrimaryFranchise(), aSecondaryFranchise()];\r\n  const interestedCreator = anInterestedCreator({\r\n    preferredEmail: updatedPreferredEmail,\r\n    country: creatorCountry,\r\n    contentLanguages: [aLanguage({ value: \"en\" })],\r\n    preferredFranchises: preferredFranchises,\r\n    preferredLanguage: aCountry({ code: \"en_US\", name: \"English\" }),\r\n    creatorTypes,\r\n    contentUrls: [{ url: Random.url() }]\r\n  });\r\n  let creatorProgram = {\r\n    code: \"affiliate\"\r\n  };\r\n  let expectedCompletedApplication = {\r\n    defaultGamerTag: interestedCreator.defaultGamerTag,\r\n    nucleusId: interestedCreator.nucleusId,\r\n    firstName: interestedCreator.firstName,\r\n    lastName: interestedCreator.lastName,\r\n    originEmail: interestedCreator.originEmail,\r\n    dateOfBirth: interestedCreator.dateOfBirth,\r\n    preferredEmail: interestedCreator.preferredEmail,\r\n    countryCode,\r\n    creatorTypes: [\r\n      (interestedCreator.creatorTypes[0] as unknown as { value: string }).value,\r\n      (interestedCreator.creatorTypes[1] as unknown as { value: string })?.value\r\n    ],\r\n    contentLanguages: [interestedCreator.contentLanguages[0].value],\r\n    additionalLinks: [interestedCreator.contentUrls[0].url],\r\n    preferredFranchises: interestedCreator.preferredFranchises,\r\n    preferredLanguage: interestedCreator.preferredLanguage,\r\n    creatorProgram: creatorProgram\r\n  };\r\n\r\n  it(\"submits an interested creator information\", async () => {\r\n    const client = { post: jest.fn().mockReturnValue({ data: expectedCompletedApplication }) };\r\n    const interestedCreatorsHttpClient = new InterestedCreatorsHttpClient(client as unknown as TraceableHttpClient);\r\n\r\n    await interestedCreatorsHttpClient.addRequestToJoinProgram({\r\n      ...interestedCreator,\r\n      creatorProgram\r\n    } as unknown as InterestedCreator);\r\n\r\n    expect(client.post).toHaveBeenCalledTimes(1);\r\n    expect(client.post).toHaveBeenCalledWith(\"/v4/creator-applications\", { body: expectedCompletedApplication });\r\n  });\r\n});\r\n"}, "__tests__/src/server/contentScanning/VerifyContentUrlController.spec.ts": {"tests": [{"id": "20", "name": "VerifyContentUrlController verify content urls successfully", "location": {"start": {"column": 4, "line": 13}}}, {"id": "21", "name": "VerifyContentUrlController verify content urls successfully with type 'CREATORS'", "location": {"start": {"column": 4, "line": 39}}}], "source": "import \"reflect-metadata\";\r\nimport { createMocks } from \"node-mocks-http\";\r\nimport { NextApiResponse } from \"next\";\r\nimport VerifyContentUrlController from \"@src/server/contentScanning/VerifyContentUrlController\";\r\nimport ContentScanningHttpClient from \"@src/server/contentScanning/ContentScanningHttpClient\";\r\nimport { NextApiRequestWithSession } from \"@eait-playerexp-cn/server-kernel\";\r\n\r\ndescribe(\"VerifyContentUrlController\", () => {\r\n  let controller: VerifyContentUrlController;\r\n\r\n  beforeEach(() => jest.clearAllMocks());\r\n\r\n  it(\"verify content urls successfully\", async () => {\r\n    const urls = [\"https://reddit.com\", \"https://google.com\"];\r\n    const body = { urls };\r\n    const { req, res } = createMocks({\r\n      method: \"POST\",\r\n      url: `/v1/secure-content/`,\r\n      body\r\n    });\r\n    const verifyContentUrlResponse = [\r\n      { url: \"https://reddit.com\", isSecure: true },\r\n      { url: \"https://google.com\", isSecure: true }\r\n    ];\r\n    const mockVerifyContentUrl = jest.fn(() => Promise.resolve(verifyContentUrlResponse));\r\n    controller = new VerifyContentUrlController(\r\n      {\r\n        verifyUrls: mockVerifyContentUrl\r\n      } as unknown as ContentScanningHttpClient,\r\n      []\r\n    );\r\n\r\n    await controller.handle(req as unknown as NextApiRequestWithSession, res as unknown as NextApiResponse);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(mockVerifyContentUrl).toHaveBeenCalledTimes(1);\r\n  });\r\n\r\n  it(\"verify content urls successfully with type 'CREATORS'\", async () => {\r\n    const urls = [\"https://reddit.com\"];\r\n    const type = \"CREATORS\";\r\n    const body = { urls };\r\n    const { req, res } = createMocks({\r\n      method: \"POST\",\r\n      url: `/api/verify-content-url`,\r\n      body,\r\n      query: { type }\r\n    });\r\n    const verifyContentUrlResponse = [{ url: \"https://reddit.com\", isSecure: true }];\r\n    const mockVerifyContentUrl = jest.fn(() => Promise.resolve(verifyContentUrlResponse));\r\n    controller = new VerifyContentUrlController(\r\n      {\r\n        verifyUrls: mockVerifyContentUrl\r\n      } as unknown as ContentScanningHttpClient,\r\n      []\r\n    );\r\n\r\n    await controller.handle(req as unknown as NextApiRequestWithSession, res as unknown as NextApiResponse);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(mockVerifyContentUrl).toHaveBeenCalledTimes(1);\r\n    expect(mockVerifyContentUrl).toHaveBeenCalledWith({ urls: [\"https://reddit.com\"] }, type);\r\n  });\r\n});\r\n"}, "__tests__/src/server/connecteAccounts/RemoveConnectedAccountController.spec.ts": {"tests": [{"id": "22", "name": "RemoveConnectedAccountController removes interested creator account", "location": {"start": {"column": 4, "line": 14}}}, {"id": "23", "name": "RemoveConnectedAccountController removes creator account", "location": {"start": {"column": 4, "line": 32}}}], "source": "import \"reflect-metadata\";\r\nimport { createMocks, MockResponse } from \"node-mocks-http\";\r\nimport { NextApiResponse } from \"next\";\r\nimport { NextApiRequestWithSession } from \"@eait-playerexp-cn/server-kernel\";\r\nimport RemoveConnectedAccountController from \"@src/server/connecteAccounts/RemoveConnectedAccountController\";\r\nimport ConnectedAccountsHttpClient from \"@src/server/connecteAccounts/ConnectedAccountsHttpClient\";\r\n\r\ndescribe(\"RemoveConnectedAccountController\", () => {\r\n  let controller: RemoveConnectedAccountController;\r\n  const session = { save: jest.fn() };\r\n\r\n  beforeEach(() => jest.clearAllMocks());\r\n\r\n  it(\"removes interested creator account\", async () => {\r\n    const nucleusId = \"************\";\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"DELETE\",\r\n      url: \"api/remove-account\",\r\n      query: { id: \"a1LDF00000K4z0i2AB\" },\r\n      session: { ...session, nucleusId }\r\n    });\r\n    const accounts = { removeConnectedAccount: jest.fn() } as unknown as ConnectedAccountsHttpClient;\r\n    controller = new RemoveConnectedAccountController(accounts);\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(accounts.removeConnectedAccount).toHaveBeenCalledTimes(1);\r\n    expect(accounts.removeConnectedAccount).toHaveBeenCalledWith(req.query.id, \"INTERESTED_CREATOR\");\r\n  });\r\n\r\n  it(\"removes creator account\", async () => {\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"DELETE\",\r\n      url: \"api/remove-account\",\r\n      query: { id: \"a1LDF00000K4z0i2AB\" },\r\n      session: { ...session, user: {} }\r\n    });\r\n    const accounts = { removeConnectedAccount: jest.fn() } as unknown as ConnectedAccountsHttpClient;\r\n    controller = new RemoveConnectedAccountController(accounts);\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(accounts.removeConnectedAccount).toHaveBeenCalledTimes(1);\r\n    expect(accounts.removeConnectedAccount).toHaveBeenCalledWith(req.query.id, \"CREATOR\");\r\n  });\r\n});\r\n"}, "__tests__/src/server/interestedCreator/UpdateApplicationController.spec.ts": {"tests": [{"id": "24", "name": "UpdateApplicationController updates an interested creator application", "location": {"start": {"column": 4, "line": 18}}}], "source": "import { createMocks, MockResponse } from \"node-mocks-http\";\r\nimport { NextApiResponse } from \"next\";\r\nimport UpdateApplicationController from \"@src/server/interestedCreator/UpdateApplicationController\";\r\nimport { aLocalizedDate } from \"@eait-playerexp-cn/interested-creators-ui\";\r\nimport { NextApiRequestWithSession } from \"@eait-playerexp-cn/server-kernel\";\r\nimport { aCountry, aLanguage } from \"@eait-playerexp-cn/metadata-test-fixtures\";\r\nimport { Country, CountryResponse } from \"@eait-playerexp-cn/metadata-types\";\r\n\r\ndescribe(\"UpdateApplicationController\", () => {\r\n  let controller: UpdateApplicationController;\r\n  const session = { save: jest.fn() };\r\n\r\n  beforeEach(() => {\r\n    jest.clearAllMocks();\r\n    controller = new UpdateApplicationController([]);\r\n  });\r\n\r\n  it(\"updates an interested creator application\", async () => {\r\n    const creator = {\r\n      nucleusId: *********,\r\n      defaultGamerTag: \"RiffleShooter\",\r\n      originEmail: \"<EMAIL>\",\r\n      dateOfBirth: aLocalizedDate().minusYears(18).build().formatWithEpoch(\"DD/MM/YYYY\")\r\n    };\r\n    const updatedCreator = {\r\n      ...creator,\r\n      contentAccounts: [], // default value\r\n      creatorTypes: [], // default value\r\n      preferredFranchises: [], // default value\r\n      firstName: \"Jane\",\r\n      lastName: \"Doe\",\r\n      country: Country.fromApi(aCountry({ name: \"Canada\" }) as unknown as CountryResponse),\r\n      contentUrls: [{ url: \"https://youtube.com\", followers: 3456789 }],\r\n      contentLanguages: [aLanguage({ value: \"en\", label: \"English\" })]\r\n    };\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"POST\",\r\n      url: \"/api/applications\",\r\n      body: updatedCreator,\r\n      session: { ...session, interestedCreator: {} }\r\n    });\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(JSON.parse(res._getData())).toEqual(updatedCreator);\r\n    expect(req.session.interestedCreator).toEqual(updatedCreator);\r\n    expect(req.session.save).toHaveBeenCalledTimes(1);\r\n  });\r\n});\r\n"}, "__tests__/src/server/interestedCreator/AddRequestToJoinProgramController.spec.ts": {"tests": [{"id": "25", "name": "AddRequestToJoinProgramController saves an interested creator information", "location": {"start": {"column": 4, "line": 15}}}], "source": "import \"reflect-metadata\";\r\nimport { createMocks } from \"node-mocks-http\";\r\nimport { NextApiResponse } from \"next\";\r\nimport InterestedCreatorsHttpClient from \"@src/server/interestedCreator/InterestedCreatorsHttpClient\";\r\nimport { anInterestedCreator } from \"@eait-playerexp-cn/interested-creators-ui\";\r\nimport { NextApiRequestWithSession } from \"@eait-playerexp-cn/server-kernel\";\r\nimport AddRequestToJoinProgramController from \"@src/server/interestedCreator/AddRequestToJoinProgramController\";\r\nimport config from \"../../../../config\";\r\n\r\njest.mock(\"../../../../config\");\r\n\r\ndescribe(\"AddRequestToJoinProgramController\", () => {\r\n  beforeEach(() => (config.PROGRAM_CODE = \"affiliate\"));\r\n\r\n  it(\"saves an interested creator information\", async () => {\r\n    const interestedCreator = anInterestedCreator();\r\n    const { req, res } = createMocks({\r\n      method: \"POST\",\r\n      url: \"/api/v4/interested-creators\",\r\n      body: interestedCreator\r\n    });\r\n    const interestedCreators = { addRequestToJoinProgram: jest.fn() };\r\n    const controller = new AddRequestToJoinProgramController(\r\n      interestedCreators as unknown as InterestedCreatorsHttpClient,\r\n      []\r\n    );\r\n\r\n    await controller.handle(req as unknown as NextApiRequestWithSession, res as unknown as NextApiResponse);\r\n\r\n    expect(res._getStatusCode()).toBe(201);\r\n    expect(interestedCreators.addRequestToJoinProgram).toHaveBeenCalledTimes(1);\r\n    expect(interestedCreators.addRequestToJoinProgram).toHaveBeenCalledWith({\r\n      ...interestedCreator,\r\n      creatorProgram: {\r\n        code: \"affiliate\"\r\n      }\r\n    });\r\n  });\r\n});\r\n"}, "__tests__/src/server/channels/facebook/ViewFacebookPagesController.spec.ts": {"tests": [{"id": "26", "name": "ViewFacebookPagesController gets all Facebook pages from session", "location": {"start": {"column": 4, "line": 12}}}, {"id": "27", "name": "ViewFacebookPagesController return 404 if pages are not in session", "location": {"start": {"column": 4, "line": 27}}}], "source": "import \"reflect-metadata\";\r\nimport { createMocks, MockResponse } from \"node-mocks-http\";\r\nimport { NextApiRequestWithSession } from \"@eait-playerexp-cn/server-kernel\";\r\nimport { NextApiResponse } from \"next\";\r\nimport ViewFacebookPagesController from \"@src/server/channels/facebook/ViewFacebookPagesController\";\r\n\r\ndescribe(\"ViewFacebookPagesController\", () => {\r\n  let controller: ViewFacebookPagesController;\r\n\r\n  beforeEach(() => jest.clearAllMocks());\r\n\r\n  it(\"gets all Facebook pages from session\", async () => {\r\n    const creatorFacebookPages = [{ id: \"2347sdhfjsd\", accessToken: \"asfdhjsadh235667887\", name: \"HariTest\" }];\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"GET\",\r\n      url: \"/api/facebook-pages\",\r\n      session: { fbPages: creatorFacebookPages }\r\n    });\r\n    controller = new ViewFacebookPagesController();\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(JSON.parse(res._getData())).toEqual(creatorFacebookPages);\r\n  });\r\n\r\n  it(\"return 404 if pages are not in session\", async () => {\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"GET\",\r\n      url: \"/api/facebook-pages\",\r\n      session: {}\r\n    });\r\n    controller = new ViewFacebookPagesController();\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(404);\r\n  });\r\n});\r\n"}, "__tests__/src/server/interestedCreator/StartApplicationController.spec.ts": {"tests": [{"id": "28", "name": "StartApplicationController redirects to log in endpoint", "location": {"start": {"column": 4, "line": 17}}}], "source": "import { createMocks, MockResponse } from \"node-mocks-http\";\r\nimport { NextApiResponse } from \"next\";\r\nimport StartApplicationController from \"@src/server/interestedCreator/StartApplicationController\";\r\nimport { NextApiRequestWithSession } from \"@eait-playerexp-cn/server-kernel\";\r\nimport config from \"../../../../config\";\r\n\r\ndescribe(\"StartApplicationController\", () => {\r\n  let controller: StartApplicationController;\r\n  const session = { save: jest.fn() };\r\n\r\n  beforeEach(() => {\r\n    jest.clearAllMocks();\r\n    controller = new StartApplicationController([]);\r\n    config.REDIRECT_URL = \"http://localhost:3040/support-a-creator\";\r\n  });\r\n\r\n  it(\"redirects to log in endpoint\", async () => {\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"GET\",\r\n      url: `/api/applications`,\r\n      session\r\n    });\r\n    res.redirect = jest.fn();\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res.redirect).toHaveBeenCalledWith(302, \"http://localhost:3040/support-a-creator/api/login\");\r\n    expect(req.session.interestedCreator).toEqual(true);\r\n    expect(req.session.save).toHaveBeenCalledTimes(1);\r\n  });\r\n});\r\n"}, "__tests__/src/server/connecteAccounts/ViewRecentErrorsController.spec.ts": {"tests": [{"id": "29", "name": "ViewRecentErrorsController shows the error stored in session", "location": {"start": {"column": 4, "line": 12}}}], "source": "import \"reflect-metadata\";\r\nimport { createMocks, MockResponse } from \"node-mocks-http\";\r\nimport { NextApiRequestWithSession } from \"@eait-playerexp-cn/server-kernel\";\r\nimport { NextApiResponse } from \"next\";\r\nimport ViewRecentErrorsController from \"@src/server/connecteAccounts/ViewRecentErrorsController\";\r\n\r\ndescribe(\"ViewRecentErrorsController\", () => {\r\n  let controller: ViewRecentErrorsController;\r\n\r\n  beforeEach(() => jest.clearAllMocks());\r\n\r\n  it(\"shows the error stored in session\", async () => {\r\n    const error = { code: \"view-facebook-pages-invalid-input\", message: \"Please try with appropriate permissions\" };\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"GET\",\r\n      url: \"/api/errors\",\r\n      session: { error }\r\n    });\r\n    controller = new ViewRecentErrorsController();\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(JSON.parse(res._getData())).toEqual(error);\r\n  });\r\n});\r\n"}, "__tests__/src/server/channels/facebook/ClearFacebookPagesController.spec.ts": {"tests": [{"id": "30", "name": "ClearFacebookPagesController removes creator's Facebook pages from the session", "location": {"start": {"column": 4, "line": 13}}}], "source": "import \"reflect-metadata\";\r\nimport { createMocks, MockResponse } from \"node-mocks-http\";\r\nimport { NextApiResponse } from \"next\";\r\nimport { NextApiRequestWithSession } from \"@eait-playerexp-cn/server-kernel\";\r\nimport ClearFacebookPagesController from \"@src/server/channels/facebook/ClearFacebookPagesController\";\r\n\r\ndescribe(\"ClearFacebookPagesController\", () => {\r\n  let controller: ClearFacebookPagesController;\r\n  const session = { save: jest.fn() };\r\n\r\n  beforeEach(() => jest.clearAllMocks());\r\n\r\n  it(\"removes creator's Facebook pages from the session\", async () => {\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"POST\",\r\n      url: \"/api/facebook-connect\",\r\n      session\r\n    });\r\n    controller = new ClearFacebookPagesController();\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(req.session.fbPages).toBeUndefined();\r\n    expect(req.session.save).toHaveBeenCalledTimes(1);\r\n  });\r\n});\r\n"}, "__tests__/src/server/connecteAccounts/ClearSessionsKeyController.spec.ts": {"tests": [{"id": "31", "name": "ClearSessionsKeyController removes 'error' from the session", "location": {"start": {"column": 4, "line": 13}}}], "source": "import \"reflect-metadata\";\r\nimport { createMocks, MockResponse } from \"node-mocks-http\";\r\nimport { NextApiResponse } from \"next\";\r\nimport { NextApiRequestWithSession } from \"@eait-playerexp-cn/server-kernel\";\r\nimport ClearSessionsKeyController from \"@src/server/connecteAccounts/ClearSessionsKeyController\";\r\n\r\ndescribe(\"ClearSessionsKeyController\", () => {\r\n  let controller: ClearSessionsKeyController;\r\n  const session = { save: jest.fn() };\r\n\r\n  beforeEach(() => jest.clearAllMocks());\r\n\r\n  it(\"removes 'error' from the session\", async () => {\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"DELETE\",\r\n      url: \"/api/errors\",\r\n      query: { key: \"error\" },\r\n      session\r\n    });\r\n    controller = new ClearSessionsKeyController();\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(req.session.error).toBeUndefined();\r\n    expect(req.session.save).toHaveBeenCalledTimes(1);\r\n  });\r\n});\r\n"}, "__tests__/src/server/connecteAccounts/ClearAccountTypeController.spec.ts": {"tests": [{"id": "32", "name": "ClearAccountTypeController removes account type from the session", "location": {"start": {"column": 4, "line": 13}}}], "source": "import \"reflect-metadata\";\r\nimport { createMocks, MockResponse } from \"node-mocks-http\";\r\nimport { NextApiResponse } from \"next\";\r\nimport { NextApiRequestWithSession } from \"@eait-playerexp-cn/server-kernel\";\r\nimport ClearAccountTypeController from \"@src/server/connecteAccounts/ClearAccountTypeController\";\r\n\r\ndescribe(\"ClearAccountTypeController\", () => {\r\n  let controller: ClearAccountTypeController;\r\n  const session = { save: jest.fn() };\r\n\r\n  beforeEach(() => jest.clearAllMocks());\r\n\r\n  it(\"removes account type from the session\", async () => {\r\n    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({\r\n      method: \"POST\",\r\n      url: \"/api/account-types\",\r\n      session\r\n    });\r\n    controller = new ClearAccountTypeController();\r\n\r\n    await controller.handle(req, res);\r\n\r\n    expect(res._getStatusCode()).toBe(200);\r\n    expect(req.session.accountType).toBeUndefined();\r\n    expect(req.session.save).toHaveBeenCalledTimes(1);\r\n  });\r\n});\r\n"}, "__tests__/src/pages/index.spec.tsx": {"tests": [{"id": "33", "name": "Index renders Index component", "location": {"start": {"column": 4, "line": 6}}}], "source": "import { render, screen } from \"@testing-library/react\";\r\nimport React from \"react\";\r\nimport Index from \"../../../src/pages/index\";\r\n\r\ndescribe(\"Index\", () => {\r\n  it(\"renders Index component\", async () => {\r\n    render(<Index />);\r\n\r\n    expect(await screen.findByText(/Applications Module Federation/i)).toBeInTheDocument();\r\n  });\r\n});\r\n"}}, "projectRoot": "C:\\Users\\<USER>\\Desktop\\creator-network\\interested-creators-micro-frontend", "config": {"$schema": "./node_modules/@stryker-mutator/core/schema/stryker-schema.json", "_comment": "Please see the guide for more information: https://stryker-mutator.io/docs/stryker-js/guides/react", "testRunner": "jest", "allowEmpty": true, "incremental": true, "jest": {"projectType": "custom", "configFile": "jest.config.js", "enableFindRelatedTests": false}, "mutate": ["src/pages/index.tsx", "src/components/**", "src/server/**"], "ignorePatterns": ["styles", "stories", "reports", "src/instrumentation*", "src/pages/api/**", "src/pages/_app.tsx", "src/pages/_document.tsx"], "thresholds": {"high": 90, "low": 70, "break": 54}, "coverageAnalysis": "perTest", "reporters": ["progress", "clear-text", "html"], "checkers": ["typescript"], "tsconfigFile": "tsconfig.json", "concurrency": 5, "timeoutMS": 120000, "tempDirName": "strykerTmp", "cleanTempDir": "always", "allowConsoleColors": true, "checkerNodeArgs": [], "commandRunner": {"command": "npm test"}, "clearTextReporter": {"allowColor": true, "allowEmojis": false, "logTests": true, "maxTestsToLog": 3, "reportTests": true, "reportMutants": true, "reportScoreTable": true, "skipFull": false}, "dashboard": {"baseUrl": "https://dashboard.stryker-mutator.io/api/reports", "reportType": "full"}, "dryRunOnly": false, "eventReporter": {"baseDir": "reports/mutation/events"}, "ignoreStatic": false, "incrementalFile": "reports/stryker-incremental.json", "force": false, "fileLogLevel": "off", "inPlace": false, "logLevel": "info", "maxConcurrentTestRunners": 9007199254740991, "maxTestRunnerReuse": 0, "mutator": {"plugins": null, "excludedMutations": []}, "plugins": ["@stryker-mutator/*"], "appendPlugins": [], "htmlReporter": {"fileName": "reports/mutation/mutation.html"}, "jsonReporter": {"fileName": "reports/mutation/mutation.json"}, "disableTypeChecks": true, "symlinkNodeModules": true, "testRunnerNodeArgs": [], "timeoutFactor": 1.5, "dryRunTimeoutMinutes": 5, "warnings": true, "disableBail": false, "ignorers": [], "typescriptChecker": {"prioritizePerformanceOverAccuracy": true}}, "framework": {"name": "StrykerJS", "version": "8.5.0", "branding": {"homepageUrl": "https://stryker-mutator.io", "imageUrl": "data:image/svg+xml;utf8,%3Csvg viewBox='0 0 1458 1458' xmlns='http://www.w3.org/2000/svg' fill-rule='evenodd' clip-rule='evenodd' stroke-linejoin='round' stroke-miterlimit='2'%3E%3Cpath fill='none' d='M0 0h1458v1458H0z'/%3E%3CclipPath id='a'%3E%3Cpath d='M0 0h1458v1458H0z'/%3E%3C/clipPath%3E%3Cg clip-path='url(%23a)'%3E%3Cpath d='M1458 729c0 402.655-326.345 729-729 729S0 1131.655 0 729C0 326.445 326.345 0 729 0s729 326.345 729 729' fill='%23e74c3c' fill-rule='nonzero'/%3E%3Cpath d='M778.349 1456.15L576.6 1254.401l233-105 85-78.668v-64.332l-257-257-44-187-50-208 251.806-82.793L1076.6 389.401l380.14 379.15c-19.681 367.728-311.914 663.049-678.391 687.599z' fill-opacity='.3'/%3E%3Cpath d='M753.4 329.503c41.79 0 74.579 7.83 97.925 25.444 23.571 18.015 41.69 43.956 55.167 77.097l11.662 28.679 165.733-58.183-14.137-32.13c-26.688-60.655-64.896-108.61-114.191-144.011-49.329-35.423-117.458-54.302-204.859-54.302-50.78 0-95.646 7.376-134.767 21.542-40.093 14.671-74.09 34.79-102.239 60.259-28.84 26.207-50.646 57.06-65.496 92.701-14.718 35.052-22.101 72.538-22.101 112.401 0 72.536 20.667 133.294 61.165 182.704 38.624 47.255 98.346 88.037 179.861 121.291 42.257 17.475 78.715 33.125 109.227 46.994 27.193 12.361 49.294 26.124 66.157 41.751 15.309 14.186 26.497 30.584 33.63 49.258 7.721 20.214 11.16 45.69 11.16 76.402 0 28.021-4.251 51.787-13.591 71.219-8.832 18.374-20.171 33.178-34.523 44.219-14.787 11.374-31.193 19.591-49.393 24.466-19.68 5.359-39.14 7.993-58.69 7.993-29.359 0-54.387-3.407-75.182-10.747-20.112-7.013-37.144-16.144-51.259-27.486-13.618-11.009-24.971-23.766-33.744-38.279-9.64-15.8-17.272-31.924-23.032-48.408l-10.965-31.376-161.669 60.585 10.734 30.124c10.191 28.601 24.197 56.228 42.059 82.748 18.208 27.144 41.322 51.369 69.525 72.745 27.695 21.075 60.904 38.218 99.481 51.041 37.777 12.664 82.004 19.159 132.552 19.159 49.998 0 95.818-8.321 137.611-24.622 42.228-16.471 78.436-38.992 108.835-67.291 30.719-28.597 54.631-62.103 71.834-100.642 17.263-38.56 25.923-79.392 25.923-122.248 0-54.339-8.368-100.37-24.208-138.32-16.29-38.759-38.252-71.661-65.948-98.797-26.965-26.418-58.269-48.835-93.858-67.175-33.655-17.241-69.196-33.11-106.593-47.533-35.934-13.429-65.822-26.601-89.948-39.525-22.153-11.868-40.009-24.21-53.547-37.309-11.429-11.13-19.83-23.678-24.718-37.664-5.413-15.49-7.98-33.423-7.98-53.577 0-40.883 11.293-71.522 37.086-90.539 28.443-20.825 64.985-30.658 109.311-30.658z' fill='%23f1c40f' fill-rule='nonzero'/%3E%3Cpath d='M720 0h18v113h-18zM1458 738v-18h-113v18h113zM720 1345h18v113h-18zM113 738v-18H0v18h113z'/%3E%3C/g%3E%3C/svg%3E"}, "dependencies": {"@stryker-mutator/jest-runner": "8.5.0", "@stryker-mutator/typescript-checker": "8.5.0", "jest": "29.7.0", "typescript": "5.5.4", "webpack": "5.94.0"}}}