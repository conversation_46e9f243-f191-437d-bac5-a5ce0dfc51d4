// import { Inject, Service } from "typedi";
// import {type HttpClient } from "@eait-playerexp-cn/http-client";
// import { AxiosResponse } from "axios";
// import InterestedCreatorApplication from "./InterestedCreatorApplication";
// import Status from "../api/Status";
// import InterestedCreatorApplicationStatus from "./InterestedCreatorApplicationStatus";

// @Service()
// class InterestedCreatorApplications {
//   constructor(@Inject("operationsClient") private client: HttpClient<AxiosResponse>) {}

//   /**
//    * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#operation/viewCreatorApplicationStatus|View Creator application status}
//    */
//   async forCreatorWith(nucleusId: number): Promise<InterestedCreatorApplication> {
//     try {
//       const response: AxiosResponse = await this.client.get(`/v1/creator-applications/${nucleusId}`);
//       return new InterestedCreatorApplication(response.data);
//     } catch (e) {
//       const status = e?.response?.status;
//       // There's no application for given nucleusId or nucleusId can't be found
//       if (status === Status.CONFLICT || status === Status.NOT_FOUND) return null;
//       throw e;
//     }
//   }

//   /**
//    * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Interested-Creators/operation/viewCreatorApplicationStatusWithApplicantInformation|View Creator application status}
//    */
//   async forCreatorWithApplicationStatus(nucleusId: number): Promise<InterestedCreatorApplicationStatus> {
//     try {
//       const response: AxiosResponse = await this.client.get(`/v2/creator-applications/${nucleusId}`);
//       // This will be called from Approved, Pending and Rejected flow.
//       // For Approved status we'll get `applicantInformation = null`
//       if (response.data.applicantInformation) {
//         response.data.applicantInformation.nucleusId = Number(response.data.applicantInformation.nucleusId);
//         response.data.applicantInformation.contentLanguages = response.data.applicantInformation.contentLanguages.map(
//           (contentLanguage) => ({ value: contentLanguage.code, label: contentLanguage.name })
//         );
//         response.data.applicantInformation.contentAccounts = response.data.applicantInformation.contentAccounts.map(
//           (contentAccount) => ({ url: contentAccount.uri, followers: contentAccount.followersCount })
//         );
//       }
//       response.data.status = response.data.requestStatus?.status;
//       return new InterestedCreatorApplicationStatus(response.data);
//     } catch (e) {
//       const status = e?.response?.status;
//       // There's no application for given nucleusId or nucleusId can't be found
//       if (status === Status.CONFLICT || status === Status.NOT_FOUND) return null;
//       throw e;
//     }
//   }
// }

// export default InterestedCreatorApplications;
