# Host Application Usage Guide

This guide shows how to integrate and use the Interested Creators MFE in your host application.

## Quick Start

### 1. Copy the Required Files

Copy these files to your host application:

```
hooks/useMFEConfiguration.ts
components/MFEConfigurationProvider.tsx
components/CreatorProgramPage.tsx
pages/_app.tsx (modify your existing _app.tsx)
```

### 2. Update Your Environment Variables

Add these to your `.env` file:

```bash
# MFE Configuration
CREATOR_PROGRAM_REDIRECT_URL=http://localhost:3000/creator-program
CREATOR_PROGRAM_CODE=affiliate
CREATORS_API_BASE_URL=https://dev-services.cn.ea.com/creators-api
APPLICATIONS_MFE_BASE_URL=http://localhost:3003/cn-applications-mfe
```

### 3. Update Your next.config.js

Add the MFE configuration variables to `publicRuntimeConfig`:

```javascript
publicRuntimeConfig: {
  // ... existing config
  CREATOR_PROGRAM_REDIRECT_URL: process.env.CREATOR_PROGRAM_REDIRECT_URL,
  CREATOR_PROGRAM_CODE: process.env.CREATOR_PROGRAM_CODE,
  CREATORS_API_BASE_URL: process.env.CREATORS_API_BASE_URL,
  APPLICATIONS_MFE_BASE_URL: process.env.APPLICATIONS_MFE_BASE_URL
}
```

## Usage Examples

### Basic Usage

```tsx
import { CreatorProgramPage } from '../components/CreatorProgramPage';

const MyCreatorPage = () => {
  return <CreatorProgramPage currentStep="start" />;
};
```

### Advanced Usage with Custom Configuration

```tsx
import { MFEConfigurationProvider } from '../components/MFEConfigurationProvider';
import { CreatorProgramStartPage } from '../components/CreatorProgramPage';

const MyCreatorPage = () => {
  const customConfig = {
    redirectUrl: 'https://my-custom-domain.com/creator-program',
    programCode: 'my-custom-program'
  };

  return (
    <MFEConfigurationProvider config={customConfig}>
      <CreatorProgramStartPage />
    </MFEConfigurationProvider>
  );
};
```

### Using Individual MFE Components

```tsx
import React, { Suspense } from 'react';
import { withMFEConfiguration } from '../components/MFEConfigurationProvider';

const InterestedCreatorsStartPage = React.lazy(() => 
  import('applications/InterestedCreatorsStartPage')
);

const MyCustomPage = withMFEConfiguration(() => {
  return (
    <div>
      <h1>Welcome to Our Creator Program</h1>
      <Suspense fallback={<div>Loading...</div>}>
        <InterestedCreatorsStartPage />
      </Suspense>
    </div>
  );
});
```

### Navigation Between MFE Pages

```tsx
import { useCreatorProgramNavigation } from '../components/CreatorProgramPage';

const MyComponent = () => {
  const { navigateToApplication, navigateToLogin, isReady } = useCreatorProgramNavigation();

  return (
    <div>
      <button 
        onClick={navigateToApplication}
        disabled={!isReady}
      >
        Start Application
      </button>
      <button 
        onClick={navigateToLogin}
        disabled={!isReady}
      >
        Login
      </button>
    </div>
  );
};
```

## Configuration Options

### Environment-Based Configuration

Set different configurations for different environments:

```bash
# Development
CREATOR_PROGRAM_REDIRECT_URL=http://localhost:3000/creator-program
CREATOR_PROGRAM_CODE=dev-affiliate

# Staging
CREATOR_PROGRAM_REDIRECT_URL=https://staging.yourapp.com/creator-program
CREATOR_PROGRAM_CODE=staging-affiliate

# Production
CREATOR_PROGRAM_REDIRECT_URL=https://yourapp.com/creator-program
CREATOR_PROGRAM_CODE=production-affiliate
```

### Runtime Configuration

Override configuration at runtime:

```tsx
const MyApp = ({ Component, pageProps }) => {
  const config = pageProps.runtimeConfiguration || {};
  
  // Override configuration based on user or tenant
  const mfeConfig = {
    redirectUrl: getUserSpecificRedirectUrl(user) || config.CREATOR_PROGRAM_REDIRECT_URL,
    programCode: getTenantProgramCode(tenant) || config.CREATOR_PROGRAM_CODE
  };

  return (
    <MFEConfigurationProvider config={mfeConfig}>
      <Component {...pageProps} />
    </MFEConfigurationProvider>
  );
};
```

## Error Handling

### Custom Error Component

```tsx
const CustomErrorComponent = (error: string, retry: () => void) => (
  <div className="custom-error">
    <h3>Oops! Something went wrong</h3>
    <p>{error}</p>
    <button onClick={retry}>Try Again</button>
  </div>
);

<MFEConfigurationProvider 
  config={config}
  errorComponent={CustomErrorComponent}
>
  {children}
</MFEConfigurationProvider>
```

### Custom Loading Component

```tsx
const CustomLoadingComponent = (
  <div className="custom-loading">
    <div className="spinner" />
    <p>Setting up your creator experience...</p>
  </div>
);

<MFEConfigurationProvider 
  config={config}
  loadingComponent={CustomLoadingComponent}
>
  {children}
</MFEConfigurationProvider>
```

## Debugging

### Development Mode

In development, you'll see debug information:

- Configuration status in browser console
- Debug panel on creator program pages
- Detailed error information

### Checking MFE Status

```tsx
import { useMFEConfigurationContext } from '../components/MFEConfigurationProvider';

const DebugComponent = () => {
  const { isReady, isLoading, error, config, status } = useMFEConfigurationContext();
  
  return (
    <div>
      <p>Ready: {isReady ? 'Yes' : 'No'}</p>
      <p>Loading: {isLoading ? 'Yes' : 'No'}</p>
      <p>Error: {error || 'None'}</p>
      <pre>{JSON.stringify({ config, status }, null, 2)}</pre>
    </div>
  );
};
```

## Best Practices

1. **Initialize Early**: The MFEConfigurationProvider should wrap your entire app or at least the parts that use MFE components.

2. **Environment Variables**: Use environment variables for configuration that changes between environments.

3. **Error Handling**: Always provide fallback UI for when MFE configuration fails.

4. **Loading States**: Show appropriate loading states while MFE is being configured.

5. **Testing**: Use the `resetMFE()` function to reset configuration between tests.

## Troubleshooting

### Common Issues

1. **MFE not loading**: Check that `APPLICATIONS_MFE_BASE_URL` is correct and the MFE is running.

2. **Configuration not taking effect**: Ensure `MFEConfigurationProvider` wraps your components.

3. **Environment variables not available**: Check that variables are added to `publicRuntimeConfig` in next.config.js.

4. **Module Federation errors**: Verify that the MFE is exposing the required services (`RuntimeConfig`, `MFEInitializer`).

### Debug Steps

1. Check browser console for configuration logs
2. Verify environment variables are set correctly
3. Ensure MFE is running and accessible
4. Check network tab for failed module federation requests
