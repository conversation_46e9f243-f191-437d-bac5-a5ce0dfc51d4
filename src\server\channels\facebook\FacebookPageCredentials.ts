export default class FacebookPageCredentials {
  static forCreator(pageId: string, pageAccessToken: string, creatorId: string): FacebookPageCredentials {
    return new FacebookPageCredentials(pageId, pageAccessToken, creatorId);
  }

  static forInterestedCreator(pageId: string, pageAccessToken: string, nucleusId: number): FacebookPageCredentials {
    return new FacebookPageCredentials(pageId, pageAccessToken, null, nucleusId);
  }

  private constructor(
    readonly pageId: string,
    readonly pageAccessToken: string,
    readonly creatorId: string,
    readonly nucleusId?: number
  ) {}
}
