import { NextApiResponse } from "next";
import { AuthenticatedRequestHandler } from "@eait-playerexp-cn/identity";
import FacebookPageCredentials from "./FacebookPageCredentials";
import { Service } from "typedi";
import { Controller, NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import { HttpStatus } from "@eait-playerexp-cn/http";
import ConnectedAccountsHttpClient from "../../connecteAccounts/ConnectedAccountsHttpClient";
import config from "../../../../config";

@Service()
class ConnectFacebookPageController extends AuthenticatedRequestHandler implements Controller {
  constructor(private readonly connectedAccounts: ConnectedAccountsHttpClient) {
    super();
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const isInterestedCreator = config.FLAG_PER_PROGRAM_PROFILE
      ? this.identity(req).type === "INTERESTED_CREATOR"
      : this.hasSession(req, "nucleusId");

    const { pageId, pageAccessToken } = req.body;
    let credentials;

    if (isInterestedCreator) {
      credentials = FacebookPageCredentials.forInterestedCreator(
        pageId as string,
        pageAccessToken as string,
        config.FLAG_PER_PROGRAM_PROFILE ? this.identity(req).nucleusId : (this.session(req, "nucleusId") as number)
      );
    } else {
      const creator = config.FLAG_PER_PROGRAM_PROFILE ? this.identity(req) : this.authenticatedUser(req);
      credentials = FacebookPageCredentials.forCreator(pageId as string, pageAccessToken as string, creator.id);
    }

    await this.removeFromSession(req, "fbPages");

    await this.connectedAccounts.connectFacebookPage(credentials);

    await this.addToSession(req, "accountType", "Facebook");

    this.empty(res, HttpStatus.OK);
  }
}

export default ConnectFacebookPageController;
