import { Activity, ActivityRecorder } from "@eait-playerexp-cn/activity-feed";
import * as Sentry from "@sentry/nextjs";
import { Extras } from "@sentry/types";

export default class SentryRecorder implements ActivityRecorder {
  record(activity: Activity): void {
    if (activity.level === "ERROR") {
      Sentry.captureException(activity.context.exception, { extra: activity.context as Extras });
    }
  }
}
