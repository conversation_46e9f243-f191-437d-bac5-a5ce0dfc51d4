import "reflect-metadata";
import { NextApiResponse } from "next";
import ApiContainer from "@src/ApiContainer";
import { createRouter } from "next-connect";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  NextApiRequestWithSession,
  verifyInterestedCreatorSession
} from "@eait-playerexp-cn/server-kernel";
import RemoveConnectedAccountController from "@src/server/connecteAccounts/RemoveConnectedAccountController";
import session from "@src/middleware/Session";
import onError from "@src/middleware/OnError";
import withCors from "@src/middleware/WithCors";
import corsPreflight from "@src/middleware/CorsPreflifight";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import config from "../../../../config";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? verifySession : verifyInterestedCreatorSession)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .delete(
    withCors(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
      const removeAccount = ApiContainer.get(RemoveConnectedAccountController);
      await removeAccount.handle(req, res);
    })
  )
  .all(corsPreflight);

export default router.handler({ onError });
