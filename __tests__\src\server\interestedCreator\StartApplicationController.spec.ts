import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiResponse } from "next";
import StartApplicationController from "@src/server/interestedCreator/StartApplicationController";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import config from "../../../../config";

describe("StartApplicationController", () => {
  let controller: StartApplicationController;
  const session = { save: jest.fn() };

  beforeEach(() => {
    jest.clearAllMocks();
    controller = new StartApplicationController([]);
    config.REDIRECT_URL = "http://localhost:3040/support-a-creator";
  });

  it("redirects to log in endpoint", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: `/api/applications`,
      session
    });
    res.redirect = jest.fn();

    await controller.handle(req, res);

    expect(res.redirect).toHaveBeenCalledWith(302, "http://localhost:3040/support-a-creator/api/login");
    expect(req.session.interestedCreator).toEqual(true);
    expect(req.session.save).toHaveBeenCalledTimes(1);
  });
});
