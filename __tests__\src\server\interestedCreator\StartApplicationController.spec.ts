import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiResponse } from "next";
import StartApplicationController from "@src/server/interestedCreator/StartApplicationController";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import config from "../../../../config";
import { runtimeConfig } from "@src/services/RuntimeConfig";

describe("StartApplicationController", () => {
  let controller: StartApplicationController;
  const session = { save: jest.fn() };

  beforeEach(() => {
    jest.clearAllMocks();
    controller = new StartApplicationController([]);
    config.REDIRECT_URL = "http://localhost:3040/support-a-creator";
    // Reset runtime config before each test
    runtimeConfig.reset();
  });

  it("redirects to log in endpoint using config fallback", async () => {
    // Debug: Check what config values are available
    console.log('Test config.REDIRECT_URL:', config.REDIRECT_URL);
    console.log('Runtime config redirect URL:', runtimeConfig.getRedirectUrl());
    console.log('Process env REDIRECT_URL:', process.env.REDIRECT_URL);

    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: `/api/applications`,
      session
    });
    res.redirect = jest.fn();

    await controller.handle(req, res);

    // Debug: Check what was actually called
    console.log('Redirect called with:', (res.redirect as jest.Mock).mock.calls[0]);

    expect(res.redirect).toHaveBeenCalledWith(302, "http://localhost:3040/support-a-creator/api/login");
    expect(req.session.interestedCreator).toEqual(true);
    expect(req.session.save).toHaveBeenCalledTimes(1);
  });

  it("redirects to log in endpoint using runtime config", async () => {
    // Initialize runtime config
    runtimeConfig.initialize({
      redirectUrl: "https://runtime-host.com/creator-program"
    });

    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: `/api/applications`,
      session
    });
    res.redirect = jest.fn();

    await controller.handle(req, res);

    expect(res.redirect).toHaveBeenCalledWith(302, "https://runtime-host.com/creator-program/api/login");
    expect(req.session.interestedCreator).toEqual(true);
    expect(req.session.save).toHaveBeenCalledTimes(1);
  });

  it("prefers runtime config over static config", async () => {
    // Initialize runtime config with different URL
    runtimeConfig.initialize({
      redirectUrl: "https://priority-host.com/creator-program"
    });

    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "GET",
      url: `/api/applications`,
      session
    });
    res.redirect = jest.fn();

    await controller.handle(req, res);

    // Should use runtime config, not the static config
    expect(res.redirect).toHaveBeenCalledWith(302, "https://priority-host.com/creator-program/api/login");
  });
});
