import "reflect-metadata";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { Random } from "@eait-playerexp-cn/interested-creators-ui";
import ConnectedAccountsHttpClient from "@src/server/connecteAccounts/ConnectedAccountsHttpClient";

describe("ConnectedAccountsHttpClient", () => {
  it("finds all connected accounts", async () => {
    const account = {
      name: Random.firstName(),
      disconnected: false,
      username: Random.string(),
      id: Random.uuid(),
      type: "YOUTUBE",
      uri: Random.url(),
      thumbnail: Random.imageUrl(),
      isExpired: true,
      accountId: Random.uuid()
    };
    const connectedAccounts = [account];
    const nucleusId = 123456;
    const client = { get: jest.fn().mockReturnValue({ data: connectedAccounts }) };
    const connectedAccountsHttpClient = new ConnectedAccountsHttpClient(client as unknown as TraceableHttpClient);

    const accounts = await connectedAccountsHttpClient.getAllConnectedAccounts(nucleusId);

    expect(accounts).toEqual(connectedAccounts);
    expect(client.get).toHaveBeenCalledTimes(1);
    expect(client.get).toHaveBeenCalledWith(`/v1/connected-accounts/${nucleusId}`);
  });

  it("finds all connected accounts with expiration status using number nucleusId", async () => {
    const account = {
      name: Random.firstName(),
      disconnected: false,
      username: Random.string(),
      id: Random.uuid(),
      type: "YOUTUBE",
      uri: Random.url(),
      thumbnail: Random.imageUrl(),
      isExpired: true,
      accountId: Random.uuid()
    };
    const connectedAccounts = [account];
    const nucleusId = 123456;
    const client = { get: jest.fn().mockReturnValue({ data: connectedAccounts }) };
    const connectedAccountsHttpClient = new ConnectedAccountsHttpClient(client as unknown as TraceableHttpClient);

    const accounts = await connectedAccountsHttpClient.getAllConnectedAccountsWithExpirationStatus(nucleusId);

    expect(accounts).toEqual(connectedAccounts);
    expect(client.get).toHaveBeenCalledTimes(1);
    expect(client.get).toHaveBeenCalledWith(`/v2/connected-accounts/${nucleusId}`);
  });

  it("finds all connected accounts with expiration status using string nucleusId", async () => {
    const account = {
      name: Random.firstName(),
      disconnected: false,
      username: Random.string(),
      id: Random.uuid(),
      type: "YOUTUBE",
      uri: Random.url(),
      thumbnail: Random.imageUrl(),
      isExpired: true,
      accountId: Random.uuid()
    };
    const connectedAccounts = [account];
    const nucleusId = "123456";
    const client = { get: jest.fn().mockReturnValue({ data: connectedAccounts }) };
    const connectedAccountsHttpClient = new ConnectedAccountsHttpClient(client as unknown as TraceableHttpClient);

    const accounts = await connectedAccountsHttpClient.getAllConnectedAccountsWithExpirationStatus(Number(nucleusId));

    expect(accounts).toEqual(connectedAccounts);
    expect(client.get).toHaveBeenCalledTimes(1);
    expect(client.get).toHaveBeenCalledWith(`/v2/connected-accounts/${nucleusId}`);
  });

  it("connects a Facebook page", async () => {
    const nucleusId = 123456;
    const credentials = {
      accessToken: Random.string(),
      creatorId: null,
      nucleusId,
      pageAccessToken: Random.string(),
      pageId: "a057717c-01f2-49de-9422-289b06649809"
    };
    const client = { post: jest.fn().mockReturnValue({ data: null }) };
    const connectedAccountsHttpClient = new ConnectedAccountsHttpClient(client as unknown as TraceableHttpClient);

    await connectedAccountsHttpClient.connectFacebookPage(credentials);

    expect(client.post).toHaveBeenCalledTimes(1);
    expect(client.post).toHaveBeenCalledWith("/v1/facebook-accounts", { body: credentials });
  });

  it("removes a connected account for an interested creator", async () => {
    const accountId = "a1LDF00000K4z0i2AB";
    const type = "INTERESTED_CREATOR";
    const client = { delete: jest.fn().mockReturnValue({ data: null }) };
    const connectedAccountsHttpClient = new ConnectedAccountsHttpClient(client as unknown as TraceableHttpClient);

    await connectedAccountsHttpClient.removeConnectedAccount(accountId, type);

    expect(client.delete).toHaveBeenCalledTimes(1);
    expect(client.delete).toHaveBeenCalledWith(`/v1/connected-accounts/${accountId}`, { query: { type } });
  });
});
