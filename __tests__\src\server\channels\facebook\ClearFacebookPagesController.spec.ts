import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import ClearFacebookPagesController from "@src/server/channels/facebook/ClearFacebookPagesController";

describe("ClearFacebookPagesController", () => {
  let controller: ClearFacebookPagesController;
  const session = { save: jest.fn() };

  beforeEach(() => jest.clearAllMocks());

  it("removes creator's Facebook pages from the session", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "POST",
      url: "/api/facebook-connect",
      session
    });
    controller = new ClearFacebookPagesController();

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(req.session.fbPages).toBeUndefined();
    expect(req.session.save).toHaveBeenCalledTimes(1);
  });
});
