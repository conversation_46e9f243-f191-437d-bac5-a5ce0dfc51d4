# Host Application Integration Guide

This document explains how host applications can configure the Interested Creators MFE dynamically.

## Overview

The MFE now supports runtime configuration through the `RuntimeConfig` service, allowing host applications to provide:
- `redirectUrl`: Custom redirect URL for authentication
- `programCode`: Program code for creator applications

## Integration Methods

### Method 1: Using MFEInitializer (Recommended)

```typescript
// In your host application
import { initializeMFE } from 'applications/MFEInitializer';

// Initialize the MFE configuration before using any components or APIs
try {
  const result = initializeMFE({
    redirectUrl: 'https://your-host-app.com/creator-program',
    programCode: 'your-program-code'
  });

  if (result.success) {
    console.log('MFE initialized successfully');
  }
} catch (error) {
  console.error('Failed to initialize MFE:', error.message);
}

// Now you can use the MFE components
const MyComponent = () => {
  const InterestedCreatorsStartPage = React.lazy(() => import('applications/InterestedCreatorsStartPage'));

  return (
    <React.Suspense fallback={<div>Loading...</div>}>
      <InterestedCreatorsStartPage />
    </React.Suspense>
  );
};
```

### Method 2: Direct RuntimeConfig Usage

```typescript
// In your host application
import { runtimeConfig } from 'applications/RuntimeConfig';

// Initialize the MFE configuration before using any components or APIs
runtimeConfig.initialize({
  redirectUrl: 'https://your-host-app.com/creator-program',
  programCode: 'your-program-code'
});

// Validate configuration
const validation = runtimeConfig.validate();
if (!validation.isValid) {
  console.error('Missing configuration:', validation.missingFields);
}
```

### Method 2: Dynamic Configuration Updates

```typescript
// You can also update configuration dynamically
import { runtimeConfig } from 'applications/RuntimeConfig';

// Update specific values
runtimeConfig.updateConfig({
  programCode: 'new-program-code'
});

// Or reinitialize completely
runtimeConfig.initialize({
  redirectUrl: 'https://different-host.com/creator-program',
  programCode: 'different-program-code'
});
```

### Method 3: Configuration Validation

```typescript
import { runtimeConfig } from 'applications/RuntimeConfig';

// Initialize configuration
runtimeConfig.initialize({
  redirectUrl: 'https://your-host-app.com/creator-program',
  programCode: 'your-program-code'
});

// Validate configuration
const validation = runtimeConfig.validate();
if (!validation.isValid) {
  console.error('MFE Configuration missing:', validation.missingFields);
  // Handle missing configuration
}
```

## Configuration Options

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `redirectUrl` | string | Yes | Base URL for authentication redirects |
| `programCode` | string | Yes | Program code for creator applications |

## Fallback Behavior

If runtime configuration is not provided, the MFE will fall back to environment variables:
- `REDIRECT_URL` environment variable
- `PROGRAM_CODE` environment variable

## Best Practices

1. **Initialize Early**: Call `runtimeConfig.initialize()` before rendering any MFE components
2. **Validate Configuration**: Use `runtimeConfig.validate()` to ensure all required fields are provided
3. **Environment-Specific Config**: Use different configurations for different environments
4. **Error Handling**: Handle cases where configuration might be missing or invalid

## Example: Complete Integration

```typescript
// host-app/src/components/CreatorProgramPage.tsx
import React, { useEffect, useState } from 'react';
import { initializeMFE, isMFEReady } from 'applications/MFEInitializer';

const CreatorProgramPage: React.FC = () => {
  const [isConfigured, setIsConfigured] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    try {
      // Initialize MFE configuration using the simplified initializer
      const result = initializeMFE({
        redirectUrl: process.env.REACT_APP_CREATOR_REDIRECT_URL || 'https://localhost:3000/creator-program',
        programCode: process.env.REACT_APP_PROGRAM_CODE || 'affiliate',
        validate: true,
        throwOnValidationError: true
      });

      if (result.success) {
        setIsConfigured(true);
      } else {
        throw new Error(result.error || 'Configuration failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Configuration error');
    }
  }, []);

  if (error) {
    return <div>Error: {error}</div>;
  }

  if (!isConfigured || !isMFEReady()) {
    return <div>Configuring MFE...</div>;
  }

  // Lazy load MFE components
  const InterestedCreatorsStartPage = React.lazy(() => import('applications/InterestedCreatorsStartPage'));

  return (
    <React.Suspense fallback={<div>Loading Creator Program...</div>}>
      <InterestedCreatorsStartPage />
    </React.Suspense>
  );
};

export default CreatorProgramPage;
```

## Example: Advanced Integration with Error Handling

```typescript
// host-app/src/hooks/useMFEConfig.ts
import { useEffect, useState } from 'react';
import { initializeMFE, getMFEStatus, resetMFE } from 'applications/MFEInitializer';

interface UseMFEConfigResult {
  isReady: boolean;
  error: string | null;
  retry: () => void;
}

export const useMFEConfig = (config: {
  redirectUrl: string;
  programCode: string;
}): UseMFEConfigResult => {
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const initialize = () => {
    try {
      setError(null);

      const result = initializeMFE({
        ...config,
        validate: true,
        throwOnValidationError: false,
        onValidationError: (missingFields) => {
          console.warn('MFE validation failed:', missingFields);
        }
      });

      if (result.success) {
        setIsReady(true);
      } else {
        setError(result.error || 'Unknown configuration error');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Configuration error');
    }
  };

  const retry = () => {
    resetMFE();
    setIsReady(false);
    initialize();
  };

  useEffect(() => {
    initialize();
  }, [config.redirectUrl, config.programCode]);

  return { isReady, error, retry };
};

// Usage in component
const CreatorProgramPage: React.FC = () => {
  const { isReady, error, retry } = useMFEConfig({
    redirectUrl: process.env.REACT_APP_CREATOR_REDIRECT_URL || 'https://localhost:3000/creator-program',
    programCode: process.env.REACT_APP_PROGRAM_CODE || 'affiliate'
  });

  if (error) {
    return (
      <div>
        <p>Error: {error}</p>
        <button onClick={retry}>Retry Configuration</button>
      </div>
    );
  }

  if (!isReady) {
    return <div>Configuring MFE...</div>;
  }

  const InterestedCreatorsStartPage = React.lazy(() => import('applications/InterestedCreatorsStartPage'));

  return (
    <React.Suspense fallback={<div>Loading Creator Program...</div>}>
      <InterestedCreatorsStartPage />
    </React.Suspense>
  );
};
```

## Troubleshooting

### Common Issues

1. **Configuration not taking effect**: Ensure `runtimeConfig.initialize()` is called before using MFE components
2. **API calls using wrong values**: The runtime configuration affects both frontend components and backend API routes
3. **Missing configuration**: Use `runtimeConfig.validate()` to check for missing required fields

### Debug Mode

In development, the runtime config service logs configuration changes to the console for debugging.
