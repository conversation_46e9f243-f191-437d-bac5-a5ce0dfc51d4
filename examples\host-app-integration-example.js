/**
 * Example: How Host Applications Can Use the Interested Creators MFE
 * 
 * This example shows different ways to integrate and configure the MFE
 * in your host application.
 */

// ============================================================================
// Method 1: Simple Integration (Recommended for most use cases)
// ============================================================================

// Import the MFE initializer
import { initializeMFE } from 'applications/MFEInitializer';

// Initialize the MFE with your application's configuration
const initializeCreatorMFE = () => {
  try {
    const result = initializeMFE({
      redirectUrl: 'https://your-host-app.com/creator-program',
      programCode: 'your-program-code'
    });
    
    if (result.success) {
      console.log('✅ MFE initialized successfully');
      return true;
    } else {
      console.error('❌ MFE initialization failed:', result.error);
      return false;
    }
  } catch (error) {
    console.error('❌ MFE initialization error:', error.message);
    return false;
  }
};

// ============================================================================
// Method 2: Advanced Integration with Custom Validation
// ============================================================================

import { initializeMFE, getMFEStatus } from 'applications/MFEInitializer';

const initializeCreatorMFEAdvanced = () => {
  const result = initializeMFE({
    redirectUrl: process.env.CREATOR_REDIRECT_URL || 'https://default-host.com/creator-program',
    programCode: process.env.CREATOR_PROGRAM_CODE || 'default-program',
    validate: true,
    throwOnValidationError: false,
    onValidationError: (missingFields) => {
      console.warn('⚠️ MFE configuration incomplete:', missingFields);
      // Handle missing configuration gracefully
      // Maybe show a configuration UI or use defaults
    }
  });

  if (!result.success) {
    console.error('❌ MFE configuration failed:', result.error);
    console.log('📊 MFE Status:', getMFEStatus());
    return false;
  }

  return true;
};

// ============================================================================
// Method 3: Runtime Configuration Updates
// ============================================================================

import { runtimeConfig } from 'applications/RuntimeConfig';

const updateMFEConfiguration = (newConfig) => {
  // You can update configuration dynamically
  runtimeConfig.updateConfig({
    redirectUrl: newConfig.redirectUrl,
    programCode: newConfig.programCode
  });

  // Validate the updated configuration
  const validation = runtimeConfig.validate();
  if (!validation.isValid) {
    console.warn('⚠️ Updated configuration is incomplete:', validation.missingFields);
    return false;
  }

  console.log('✅ MFE configuration updated successfully');
  return true;
};

// ============================================================================
// Method 4: React Hook Integration
// ============================================================================

import React, { useEffect, useState } from 'react';
import { initializeMFE, isMFEReady, resetMFE } from 'applications/MFEInitializer';

const useMFEConfiguration = (config) => {
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState(null);

  const initialize = () => {
    try {
      const result = initializeMFE(config);
      if (result.success && isMFEReady()) {
        setIsReady(true);
        setError(null);
      } else {
        setError(result.error || 'Configuration failed');
      }
    } catch (err) {
      setError(err.message);
    }
  };

  const retry = () => {
    resetMFE();
    setIsReady(false);
    setError(null);
    initialize();
  };

  useEffect(() => {
    initialize();
  }, [config.redirectUrl, config.programCode]);

  return { isReady, error, retry };
};

// Usage in React component
const CreatorProgramPage = () => {
  const { isReady, error, retry } = useMFEConfiguration({
    redirectUrl: 'https://your-host-app.com/creator-program',
    programCode: 'your-program-code'
  });

  if (error) {
    return (
      <div>
        <p>Configuration Error: {error}</p>
        <button onClick={retry}>Retry</button>
      </div>
    );
  }

  if (!isReady) {
    return <div>Configuring Creator Program...</div>;
  }

  // Load MFE components after configuration is ready
  const InterestedCreatorsStartPage = React.lazy(() => 
    import('applications/InterestedCreatorsStartPage')
  );

  return (
    <React.Suspense fallback={<div>Loading...</div>}>
      <InterestedCreatorsStartPage />
    </React.Suspense>
  );
};

// ============================================================================
// Method 5: Multiple Environment Configuration
// ============================================================================

const getEnvironmentConfig = () => {
  const env = process.env.NODE_ENV || 'development';
  
  const configs = {
    development: {
      redirectUrl: 'http://localhost:3000/creator-program',
      programCode: 'dev-program'
    },
    staging: {
      redirectUrl: 'https://staging.yourapp.com/creator-program',
      programCode: 'staging-program'
    },
    production: {
      redirectUrl: 'https://yourapp.com/creator-program',
      programCode: 'production-program'
    }
  };

  return configs[env] || configs.development;
};

const initializeForEnvironment = () => {
  const config = getEnvironmentConfig();
  return initializeMFE(config);
};

// ============================================================================
// Export functions for use in your application
// ============================================================================

export {
  initializeCreatorMFE,
  initializeCreatorMFEAdvanced,
  updateMFEConfiguration,
  useMFEConfiguration,
  initializeForEnvironment,
  CreatorProgramPage
};
