# Existing environment variables
GTM_AUTH=
GTM_PREVIEW=
AMPLITUDE_API_KEY=
AMPLITUDE_ENV=
RELEASE_VERSION=
SENTRY_DSN=
APP_ENV=development
APP_DEBUG=true
INITIAL_MESSAGE_TITLE=
INITIAL_MESSAGE_DESCRIPTION=
SUPPORTED_LOCALES=["en-us"]
ANALYTICS_SAMPLE_RATE=1.0
HTTP_REQUEST_TIMEOUT=60000
FLAG_OBSERVABILITY=false
METADATA_API_BASE_URL=https://dev-services.cn.ea.com/metadata-api
SERVICE_NAME=creator-hub
NOTIFICATIONS_MFE_BASE_URL=http://localhost:3001
NOTIFICATIONS_MFE_NAME=notifications
FLAG_NEW_NAVIGATION_ENABLED=true

# MFE Configuration - Add these new variables
# URL where users will be redirected for authentication in the creator program
CREATOR_PROGRAM_REDIRECT_URL=http://localhost:3000/creator-program

# Program code for the creator program (e.g., 'affiliate', 'partner', 'creator')
CREATOR_PROGRAM_CODE=affiliate

# API endpoints
CREATORS_API_BASE_URL=https://dev-services.cn.ea.com/creators-api
APPLICATIONS_MFE_BASE_URL=http://localhost:3003/cn-applications-mfe

# Optional: Override these for different environments
# Development
# CREATOR_PROGRAM_REDIRECT_URL=http://localhost:3000/creator-program
# CREATOR_PROGRAM_CODE=dev-affiliate

# Staging
# CREATOR_PROGRAM_REDIRECT_URL=https://staging.yourapp.com/creator-program
# CREATOR_PROGRAM_CODE=staging-affiliate

# Production
# CREATOR_PROGRAM_REDIRECT_URL=https://yourapp.com/creator-program
# CREATOR_PROGRAM_CODE=production-affiliate
