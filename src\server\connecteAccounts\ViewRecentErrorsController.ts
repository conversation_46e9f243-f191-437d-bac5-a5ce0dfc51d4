import { NextApiResponse } from "next";
import { Service } from "typedi";
import { <PERSON>, NextApiRequestWithSession, RequestHandler } from "@eait-playerexp-cn/server-kernel";

@Service()
class ViewRecentErrorsController extends RequestHandler implements Controller {
  constructor() {
    super();
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const error = this.session(req, "error");
    this.json(res, error);
  }
}

export default ViewRecentErrorsController;
