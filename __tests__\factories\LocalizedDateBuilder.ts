import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";

class LocalizedDateBuilder {
  public static aLocalizedDate() {
    return new LocalizedDateBuilder(new LocalizedDate(Date.now()));
  }

  public constructor(private date: LocalizedDate) {
    this.date = date;
  }

  public minusYears(year: number) {
    this.date = this.date.subtract(year, "year");
    return this;
  }

  public plusDays(days: number) {
    this.date = this.date.add(days, "days");
    return this;
  }

  minusDays(days: number) {
    this.date = this.date.subtract(days, "days");
    return this;
  }

  public build(): LocalizedDate {
    return this.date;
  }
}

export function aLocalizedDate(): LocalizedDateBuilder {
  return new LocalizedDateBuilder(new LocalizedDate(Date.now()));
}
