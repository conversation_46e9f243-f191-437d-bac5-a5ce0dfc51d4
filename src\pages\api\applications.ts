import "reflect-metadata";
import { NextApiResponse } from "next";
import { createRouter } from "next-connect";
import ApiContainer from "@src/ApiContainer";
import LegacyUpdateApplicationController from "@src/server/interestedCreator/LegacyUpdateApplicationController";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  NextApiRequestWithSession,
  verifyInterestedCreatorSession
} from "@eait-playerexp-cn/server-kernel";
import corsPreflight from "@src/middleware/CorsPreflifight";
import { addTelemetryInformation, verifySession } from "@eait-playerexp-cn/identity";
import session from "@src/middleware/Session";
import config from "config";
import onError from "@src/middleware/OnError";
import LegacyStartApplicationController from "@src/server/interestedCreator/StartApplicationController";
import { StartApplicationController } from "@eait-playerexp-cn/interested-creators-authentication-plugins";
import UpdateApplicationController from "@src/server/interestedCreator/UpdateApplicationController";
import withCors from "@src/middleware/WithCors";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  // GET request occurs with unauthenticated users, skip the check in that case
  .use(async (req, res, next) => {
    req.method === "GET"
      ? next()
      : await (config.FLAG_PER_PROGRAM_PROFILE ? verifySession : verifyInterestedCreatorSession)(
          req as unknown as NextApiRequestWithSession,
          res,
          next
        );
  })
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .get(
    withCors(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
      const controller = ApiContainer.get(
        config.FLAG_PER_PROGRAM_PROFILE ? StartApplicationController : LegacyStartApplicationController
      );
      await controller.handle(req, res);
    })
  )
  .post(
    withCors(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
      const controller = ApiContainer.get(
        config.FLAG_PER_PROGRAM_PROFILE ? UpdateApplicationController : LegacyUpdateApplicationController
      );
      await controller.handle(req, res);
    })
  )
  .all(corsPreflight);

export default router.handler({ onError });
