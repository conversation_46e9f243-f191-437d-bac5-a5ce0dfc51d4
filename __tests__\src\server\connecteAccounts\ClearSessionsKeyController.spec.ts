import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import ClearSessionsKeyController from "@src/server/connecteAccounts/ClearSessionsKeyController";

describe("ClearSessionsKeyController", () => {
  let controller: ClearSessionsKeyController;
  const session = { save: jest.fn() };

  beforeEach(() => jest.clearAllMocks());

  it("removes 'error' from the session", async () => {
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "DELETE",
      url: "/api/errors",
      query: { key: "error" },
      session
    });
    controller = new ClearSessionsKeyController();

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(req.session.error).toBeUndefined();
    expect(req.session.save).toHaveBeenCalledTimes(1);
  });
});
