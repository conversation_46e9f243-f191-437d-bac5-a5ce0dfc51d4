ARG ARTIFACTORY
ARG NODE_VERSION
FROM $ARTIFACTORY/node:$NODE_VERSION AS build-stage

# The following environment variables are for Sentry release and source maps generation
# ARG SENTRY_AUTH_TOKEN
# ENV SENTRY_AUTH_TOKEN $SENTRY_AUTH_TOKEN
ARG RELEASE_VERSION
ENV RELEASE_VERSION $RELEASE_VERSION
ENV SENTRY_PROPERTIES sentry.properties
# Disable Next.js build telemtry
ENV NEXT_TELEMETRY_DISABLED 1

WORKDIR /app
COPY . .
RUN cp .env.dist .env && \
  npm run build && \
  # npm install -g @sentry/cli@2.32.1 && \
  # sentry-cli sourcemaps inject --release=${RELEASE_VERSION} .next/server/chunks &&  \
  # sentry-cli sourcemaps upload --release=${RELEASE_VERSION} .next/server/chunks &&  \
  rm .env

FROM $ARTIFACTORY/node:$NODE_VERSION AS server-build
WORKDIR /app
COPY --from=build-stage /app/.next/standalone ./
COPY --from=build-stage /app/.next/static ./.next/static
COPY --from=build-stage /app/public ./public
COPY --from=build-stage /app/entrypoint.sh ./entrypoint.sh

RUN apk add --no-cache jq=1.7.1-r0 && \
  addgroup -S nextjs && \
  adduser -S nextjs -G nextjs && \
  chown -R nextjs:nextjs /app

USER nextjs:nextjs

EXPOSE 3003
ENV PORT 3003

# Define the health check
HEALTHCHECK --interval=1m --timeout=10s --start-period=5s --retries=3 \
CMD ["sh", "curl -f http://localhost:3003/cn-applications-mfe || exit 1"]

ENTRYPOINT ["/app/entrypoint.sh"]
