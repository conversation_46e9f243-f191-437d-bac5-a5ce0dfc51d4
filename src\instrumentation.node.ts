import { NodeSDK } from "@opentelemetry/sdk-node";
import { Resource } from "@opentelemetry/resources";
import {
  ATTR_SERVICE_NAME,
  ATTR_SERVICE_VERSION,
  SEMRESATTRS_DEPLOYMENT_ENVIRONMENT
} from "@opentelemetry/semantic-conventions";
import { OTLPTraceExporter } from "@opentelemetry/exporter-trace-otlp-grpc";
import { NoOpSpanExporter } from "@eait-playerexp-cn/telemetry";
import { BatchSpanProcessor } from "@opentelemetry/sdk-trace-node";

// OTLPTraceExporter's default URL is http://localhost:4317/v1/traces
const traceExporter =
  process.env.FLAG_OBSERVABILITY === "true"
    ? new OTLPTraceExporter({ url: process.env.OTLP_TRACE_EXPORTER_URL })
    : new NoOpSpanExporter();

const sdk = new NodeSDK({
  resource: Resource.default().merge(
    new Resource({
      [ATTR_SERVICE_NAME]: "cn-applications-mfe",
      [SEMRESATTRS_DEPLOYMENT_ENVIRONMENT]: process.env.APP_ENV || "local",
      [ATTR_SERVICE_VERSION]: process.env.RELEASE_VERSION || "1.0.0.dev"
    })
  ),
  spanProcessors: [new BatchSpanProcessor(traceExporter)]
});
sdk.start();

process.on("SIGTERM", () => {
  sdk
    .shutdown()
    .then(() => console.log("Tracing terminated"))
    .catch((error) => console.log("Error terminating tracing", error))
    .finally(() => process.exit(0));
});
