import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import LegacyUpdateApplicationController from "@src/server/interestedCreator/LegacyUpdateApplicationController";
import { aLocalizedDate } from "__tests__/factories/LocalizedDateBuilder";
import { aCountry, aLanguage } from "@eait-playerexp-cn/metadata-test-fixtures";

describe("LegacyUpdateApplicationController", () => {
  let controller: LegacyUpdateApplicationController;
  const session = { save: jest.fn() };

  beforeEach(() => {
    jest.clearAllMocks();
    controller = new LegacyUpdateApplicationController();
  });

  it("updates an interested creator application", async () => {
    const creator = {
      nucleusId: *********,
      defaultGamerTag: "RiffleShooter",
      originEmail: "<EMAIL>",
      dateOfBirth: aLocalizedDate().minusYears(18).build().formatWithEpoch("DD/MM/YYYY")
    };
    const updatedCreator = {
      ...creator,
      contentAccounts: [],
      creatorTypes: [],
      preferredFranchises: [],
      firstName: "Jane",
      lastName: "Doe",
      country: aCountry({ label: "Canada" }),
      contentUrls: [{ url: "https://youtube.com", followers: 3456789 }],
      contentLanguages: [aLanguage({ value: "en", label: "English" })]
    };
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "PUT",
      url: "/api/legacy/applications",
      body: updatedCreator,
      session: {
        ...session,
        interestedCreator: creator,
        creatorNucleusId: "*********"
      }
    });

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(updatedCreator);
    expect(req.session.interestedCreator).toEqual(updatedCreator);
    expect(req.session.creatorNucleusId).toBeUndefined();
    expect(req.session.save).toHaveBeenCalledTimes(2);
  });

  it("creates new interested creator when no user exists in session", async () => {
    const updatedCreator = {
      firstName: "Jane",
      lastName: "Doe",
      country: aCountry({ label: "Canada" }),
      contentUrls: [{ url: "https://youtube.com", followers: 3456789 }],
      contentLanguages: [aLanguage({ value: "en", label: "English" })],
      creatorTypes: [],
      preferredFranchises: []
    };
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "PUT",
      url: "/api/legacy/applications",
      body: updatedCreator,
      session: { ...session }
    });

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(updatedCreator);
    expect(req.session.interestedCreator).toEqual(updatedCreator);
    expect(req.session.save).toHaveBeenCalledTimes(1);
  });
});
