import { Inject, Service } from "typedi";
import { NextApiResponse } from "next";
import InterestedCreatorsHttpClient from "./InterestedCreatorsHttpClient";
import InterestedCreator from "./InterestedCreator";
import { Controller, NextApiRequestWithSession, RequestHandler } from "@eait-playerexp-cn/server-kernel";
import config from "config";
import { runtimeConfig } from "@src/services/RuntimeConfig";

@Service()
class AddRequestToJoinProgramController extends RequestHandler implements Controller {
  constructor(
    private readonly interestedCreators: InterestedCreatorsHttpClient,
    @Inject("supportedLocales") supportedLocales: string[]
  ) {
    super({ supportedLocales });
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    // Use runtime config with fallback to static config
    const runtimeProgramCode = runtimeConfig.getProgramCode();
    const programCode = runtimeProgramCode || config.PROGRAM_CODE;

    await this.interestedCreators.addRequestToJoinProgram({
      ...req.body,
      creatorProgram: {
        code: programCode
      }
    } as InterestedCreator);
    this.empty(res);
  }
}

export default AddRequestToJoinProgramController;
