import { Inject, Service } from "typedi";
import { NextApiResponse } from "next";
import InterestedCreatorsHttpClient from "./InterestedCreatorsHttpClient";
import InterestedCreator from "./InterestedCreator";
import { Controller, NextApiRequestWithSession, RequestHandler } from "@eait-playerexp-cn/server-kernel";
import config from "config";

@Service()
class AddRequestToJoinProgramController extends Request<PERSON>andler implements Controller {
  constructor(
    private readonly interestedCreators: InterestedCreatorsHttpClient,
    @Inject("supportedLocales") supportedLocales: string[]
  ) {
    super({ supportedLocales });
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    await this.interestedCreators.addRequestToJoinProgram({
      ...req.body,
      creatorProgram: {
        code: config.PROGRAM_CODE
      }
    } as InterestedCreator);
    this.empty(res);
  }
}

export default AddRequestToJoinProgramController;
