import React, { <PERSON> } from "react";
import { SvgProps } from "@eait-playerexp-cn/core-ui-kit";

const SvgToastClose: FC<SvgProps> = (props) => {
  return (
    <svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10 18.5C14.6944 18.5 18.5 14.6944 18.5 10C18.5 5.30558 14.6944 1.5 10 1.5C5.30558 1.5 1.5 5.30558 1.5 10C1.5 14.6944 5.30558 18.5 10 18.5ZM10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0C4.47715 0 0 4.47715 0 10C0 15.5228 4.47715 20 10 20Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.0711 6.91924L7 13.9903L6.08076 13.0711L13.1518 6L14.0711 6.91924Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6 6.99028L13.0711 14.0614L13.9903 13.1421L6.91924 6.07104L6 6.99028Z"
        fill="white"
      />
    </svg>
  );
};

SvgToastClose.defaultProps = {
  className: "icon"
};

export default SvgToastClose;
