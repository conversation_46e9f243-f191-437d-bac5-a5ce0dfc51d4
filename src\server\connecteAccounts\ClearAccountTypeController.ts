import { NextApiResponse } from "next";
import { Service } from "typedi";
import { <PERSON>, NextApiRequestWithSession, RequestHandler } from "@eait-playerexp-cn/server-kernel";
import { HttpStatus } from "@eait-playerexp-cn/http";

@Service()
class ClearAccountTypeController extends Request<PERSON>andler implements Controller {
  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    await this.removeFromSession(req, "accountType");
    this.empty(res, HttpStatus.OK);
  }
}

export default ClearAccountTypeController;
