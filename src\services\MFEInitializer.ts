/**
 * MFE Initializer - Simplified initialization for host applications
 */

import { runtimeConfig, MFEConfig } from './RuntimeConfig';

export interface InitializationOptions extends MFEConfig {
  /**
   * Whether to validate configuration after initialization
   * @default true
   */
  validate?: boolean;
  
  /**
   * Whether to throw an error if validation fails
   * @default true
   */
  throwOnValidationError?: boolean;
  
  /**
   * Custom validation callback
   */
  onValidationError?: (missingFields: string[]) => void;
}

export interface InitializationResult {
  success: boolean;
  error?: string;
  missingFields?: string[];
}

/**
 * Initialize the MFE with configuration from host application
 */
export function initializeMFE(options: InitializationOptions): InitializationResult {
  try {
    const {
      validate = true,
      throwOnValidationError = true,
      onValidationError,
      ...config
    } = options;

    // Initialize runtime configuration
    runtimeConfig.initialize(config);

    // Validate if requested
    if (validate) {
      const validation = runtimeConfig.validate();
      
      if (!validation.isValid) {
        const error = `MFE configuration validation failed. Missing fields: ${validation.missingFields.join(', ')}`;
        
        // Call custom validation error handler if provided
        if (onValidationError) {
          onValidationError(validation.missingFields);
        }
        
        if (throwOnValidationError) {
          throw new Error(error);
        }
        
        return {
          success: false,
          error,
          missingFields: validation.missingFields
        };
      }
    }

    return { success: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown initialization error';
    
    if (options.throwOnValidationError !== false) {
      throw error;
    }
    
    return {
      success: false,
      error: errorMessage
    };
  }
}

/**
 * Check if MFE is properly initialized and configured
 */
export function isMFEReady(): boolean {
  if (!runtimeConfig.isInitialized()) {
    return false;
  }
  
  const validation = runtimeConfig.validate();
  return validation.isValid;
}

/**
 * Get current MFE configuration status
 */
export function getMFEStatus(): {
  initialized: boolean;
  configured: boolean;
  config: MFEConfig;
  validation: { isValid: boolean; missingFields: string[] };
} {
  return {
    initialized: runtimeConfig.isInitialized(),
    configured: runtimeConfig.isInitialized(),
    config: runtimeConfig.getConfig(),
    validation: runtimeConfig.validate()
  };
}

/**
 * Reset MFE configuration (useful for testing or re-initialization)
 */
export function resetMFE(): void {
  runtimeConfig.reset();
}

// Export for convenience
export { runtimeConfig } from './RuntimeConfig';
export type { MFEConfig } from './RuntimeConfig';
