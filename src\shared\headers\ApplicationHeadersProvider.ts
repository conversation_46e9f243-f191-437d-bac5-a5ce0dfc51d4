import { HeadersProvider, HttpHeaders } from "@eait-playerexp-cn/http-client";
import { MDC } from "@eait-playerexp-cn/activity-logger";

export default class ApplicationHeadersProvider implements HeadersProvider {
  headers(): Promise<HttpHeaders> {
    return Promise.resolve({
      Accept: "application/json",
      "Content-Type": "application/json",
      "x-user-id": MDC.get("userId") as string,
      "x-client-id": "metadata-api"
    });
  }
}
