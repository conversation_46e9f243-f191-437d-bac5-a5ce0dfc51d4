import { Container } from "typedi";
import { AuthorizationErrorHandler, VerifyAccessToProgramServerPropsMiddleware } from "@eait-playerexp-cn/identity";
import config from "../config";
import { ActivityFeed } from "@eait-playerexp-cn/activity-feed";
import { ActivityLogger, ContextSerializer, Logger } from "@eait-playerexp-cn/activity-logger";
import { CompositeSerializer, ExceptionSerializer, ObjectMapper } from "@eait-playerexp-cn/object-mapper";
import {
  anAuthorizationHeaderProvider,
  anHttpClient,
  anOAuthTokenProvider,
  aTracer,
  CompositeHeadersProvider,
  HttpClientTracer,
  OAuthBearerHeaderProvider,
  OAuthTokenProvider
} from "@eait-playerexp-cn/http-client";
import TelemetryRecorder from "@src/shared/logging/TelemetryRecorder";
import ApplicationHeadersProvider from "@src/shared/headers/ApplicationHeadersProvider";
import CachedAccessTokenProvider from "@src/shared/tokens/CachedAccessTokenProvider";
import SentryRecorder from "@src/shared/logging/SentryRecorder";
import {
  ApiRouteErrorHandler,
  JsonErrorHandler,
  AuthorizationErrorHandler as LegacyAuthorizationErrorHandler,
  MicroServiceErrorHandler,
  RedisCache,
  RedisClientFactory,
  RequestFactory,
  SessionOptionsFactory
} from "@eait-playerexp-cn/server-kernel";

const ApiContainer = Container.of("api");

ApiContainer.set(ExceptionSerializer, new ExceptionSerializer());
ApiContainer.set(CompositeSerializer, new CompositeSerializer([ApiContainer.get(ExceptionSerializer)]));
ApiContainer.set(ObjectMapper, new ObjectMapper(ApiContainer.get(CompositeSerializer)));
ApiContainer.set(ContextSerializer, new ContextSerializer(ApiContainer.get(ObjectMapper)));
ApiContainer.set(
  ActivityFeed,
  new ActivityFeed([
    new TelemetryRecorder(),
    new ActivityLogger(new Logger(config.LOG_LEVEL), ApiContainer.get(ContextSerializer)),
    new SentryRecorder()
  ])
);
ApiContainer.set(
  HttpClientTracer,
  aTracer()
    .withName("cn-applications-mfe")
    .withSerializer(ApiContainer.get(CompositeSerializer))
    .withLoggingCollector(ApiContainer.get(ActivityFeed))
    .build()
);
ApiContainer.set(
  "sessionOptions",
  new SessionOptionsFactory(config.sessionOptions).create(RedisClientFactory.create(config.redisClient))
);
ApiContainer.set(RedisCache, new RedisCache(config.cacheOptions, ApiContainer.get(ActivityFeed)));
ApiContainer.set("corsConfiguration", config.corsConfiguration);
ApiContainer.set("supportedLocales", config.SUPPORTED_LOCALES);
ApiContainer.set("options", {
  supportedLocales: config.SUPPORTED_LOCALES,
  program: config.PROGRAM_CODE,
  feed: ApiContainer.get(ActivityFeed)
});
ApiContainer.set(
  OAuthTokenProvider,
  anOAuthTokenProvider()
    .withBaseUrl(config.ACCESS_TOKEN_BASE_URL)
    .withRequestTimeout(+config.HTTP_REQUEST_TIMEOUT)
    .withClientCredentials(config.API_CLIENT_ID, config.API_CLIENT_SECRET)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .build()
);
ApiContainer.set(
  CachedAccessTokenProvider,
  new CachedAccessTokenProvider(ApiContainer.get(OAuthTokenProvider), ApiContainer.get(RedisCache))
);
ApiContainer.set(
  OAuthBearerHeaderProvider,
  anAuthorizationHeaderProvider().withOAuthTokenProvider(ApiContainer.get(CachedAccessTokenProvider)).build()
);
ApiContainer.set(RequestFactory, new RequestFactory());
ApiContainer.set(
  JsonErrorHandler,
  config.FLAG_PER_PROGRAM_PROFILE
    ? new JsonErrorHandler([
        new MicroServiceErrorHandler(),
        new AuthorizationErrorHandler(ApiContainer.get("options"), ApiContainer.get(RequestFactory)),
        new ApiRouteErrorHandler(
          ApiContainer.get("options"),
          config.APP_DEBUG,
          ApiContainer.get(ExceptionSerializer),
          ApiContainer.get(RequestFactory)
        )
      ])
    : new JsonErrorHandler([
        new MicroServiceErrorHandler(),
        new LegacyAuthorizationErrorHandler(ApiContainer.get("options"), ApiContainer.get(RequestFactory)),
        new ApiRouteErrorHandler(
          ApiContainer.get("options"),
          config.APP_DEBUG,
          ApiContainer.get(ExceptionSerializer),
          ApiContainer.get(RequestFactory)
        )
      ])
);
ApiContainer.set(
  "contentScanningClient",
  anHttpClient()
    .withBaseUrl(config.CONTENT_SCANNING_API_BASE_URL)
    .withRequestTimeout(config.HTTP_REQUEST_TIMEOUT)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .withHeadersProvider(
      new CompositeHeadersProvider([ApiContainer.get(OAuthBearerHeaderProvider), new ApplicationHeadersProvider()])
    )
    .build()
);
ApiContainer.set(
  "contentSubmissionClient",
  anHttpClient()
    .withBaseUrl(config.CONTENT_SUBMISSION_BASE_URL)
    .withRequestTimeout(config.HTTP_REQUEST_TIMEOUT)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .withHeadersProvider(
      new CompositeHeadersProvider([ApiContainer.get(OAuthBearerHeaderProvider), new ApplicationHeadersProvider()])
    )
    .build()
);
ApiContainer.set(
  "operationsClient",
  anHttpClient()
    .withBaseUrl(config.OPERATIONS_API_BASE_URL)
    .withRequestTimeout(config.HTTP_REQUEST_TIMEOUT)
    .withTracer(ApiContainer.get(HttpClientTracer))
    .withHeadersProvider(
      new CompositeHeadersProvider([ApiContainer.get(OAuthBearerHeaderProvider), new ApplicationHeadersProvider()])
    )
    .build()
);
ApiContainer.set(
  VerifyAccessToProgramServerPropsMiddleware,
  new VerifyAccessToProgramServerPropsMiddleware(ApiContainer.get("options"))
);
export default ApiContainer;
