import { NextApiResponse } from "next";
import { Service } from "typedi";
import { <PERSON>, NextApiRequestWithSession, RequestHandler } from "@eait-playerexp-cn/server-kernel";
import { HttpStatus } from "@eait-playerexp-cn/http";

@Service()
class ClearSessionsKeyController extends RequestHandler implements Controller {
  constructor() {
    super();
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const sessionKey = req.query.key as string;
    await this.removeFromSession(req, sessionKey);
    this.empty(res, HttpStatus.OK);
  }
}

export default ClearSessionsKeyController;
