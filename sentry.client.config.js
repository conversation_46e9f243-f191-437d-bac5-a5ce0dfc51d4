// This file configures the initialization of Sen<PERSON> on the browser.
// The config you add here will be used whenever a page is visited.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";
import getConfig from "next/config";

const { publicRuntimeConfig: props = {} } = getConfig();
const { SENTRY_DSN, APP_ENV, BUILD_VERSION } = props;

Sentry.init({
  dsn: APP_ENV === "docker" ? undefined : SENTRY_DSN,
  tracesSampleRate: process.env.APP_ENV === "prod" ? 0.8 : 1.0,
  environment: APP_ENV === "docker" ? undefined : APP_ENV,
  release: APP_ENV === "docker" ? undefined : BUILD_VERSION
});
