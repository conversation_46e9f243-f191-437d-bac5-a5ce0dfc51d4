const nextJest = require("next/jest");

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files in your test environment
  dir: "./"
});

// Add any custom config to be passed to Jest
const customJestConfig = {
  setupFilesAfterEnv: ["<rootDir>/jest.setup.ts"],
  testEnvironment: "jsdom",
  bail: 1,
  verbose: true,
  transformIgnorePatterns: ["/node_modules/", "^.+\\.module\\.(css|sass|scss)$"],
  testPathIgnorePatterns: ["/node_modules/", "/.next/", "__tests__/factories", "__tests__/doubles"],
  collectCoverageFrom: [
    "src/**/*.{js,jsx,ts,tsx}",
    "!src/middleware/**/*.{js,jsx,ts,tsx}",
    "!src/pages/**/*.{js,jsx,ts,tsx}",
    "!src/shared/**/*.{ts,tsx}",
    "!src/ApiContainer.ts",
    "!src/instrumentation.*",
    "!**/*.d.ts",
    "!**/node_modules/**"
  ],

  coveragePathIgnorePatterns: ["/node_modules/"],
  coverageDirectory: "reports/coverage",
  coverageReporters: ["text", "text-summary", "html"],
  coverageThreshold: {
    global: {
      statements: 84,
      branches: 63,
      functions: 97,
      lines: 87
    }
  },
  reporters: [
    "default",
    [
      "jest-html-reporters",
      { inlineSource: true, publicPath: "reports/tests", filename: "report.html", enableMergeData: true }
    ]
  ]
};

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig);
