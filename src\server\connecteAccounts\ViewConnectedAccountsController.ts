import { NextApiResponse } from "next";
import { Service } from "typedi";
import { Controller, NextApiRequestWithSession, RequestHandler } from "@eait-playerexp-cn/server-kernel";
import ConnectedAccountsHttpClient from "./ConnectedAccountsHttpClient";
import config from "config";

@Service()
class ViewConnectedAccountsController extends Request<PERSON><PERSON><PERSON> implements Controller {
  constructor(private connectedAccounts: ConnectedAccountsHttpClient) {
    super();
  }

  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const nucleusId = Number(req.query.nucleusId);
    const accounts = config.INTERESTED_CREATOR_REAPPLY_PERIOD
      ? await this.connectedAccounts.getAllConnectedAccountsWithExpirationStatus(nucleusId)
      : await this.connectedAccounts.getAllConnectedAccounts(nucleusId);

    await this.addToSession(req, "nucleusId", nucleusId);

    this.json(res, accounts);
  }
}

export default ViewConnectedAccountsController;
