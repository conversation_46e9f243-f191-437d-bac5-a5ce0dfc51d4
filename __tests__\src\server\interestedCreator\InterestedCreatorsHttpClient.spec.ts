import "reflect-metadata";
import {
  Random,
  aSecondaryFranchise,
  aPrimaryFranchise,
  anInterestedCreator
} from "@eait-playerexp-cn/interested-creators-ui";
import { aCountry, aCreatorType, aLanguage } from "@eait-playerexp-cn/metadata-test-fixtures";
import InterestedCreatorsHttpClient from "@src/server/interestedCreator/InterestedCreatorsHttpClient";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { Country, CountryResponse } from "@eait-playerexp-cn/metadata-types";
import InterestedCreator from "@src/server/interestedCreator/InterestedCreator";

describe("InterestedCreatorsHttpClient", () => {
  const creatorTypes = [aCreatorType(), aCreatorType()];
  const countryCode = "US";
  const creatorCountry = Country.fromApi(aCountry({ code: countryCode }) as unknown as CountryResponse);
  const updatedPreferredEmail = Random.email();
  const preferredFranchises = [aPrimaryFranchise(), aSecondaryFranchise()];
  const interestedCreator = anInterestedCreator({
    preferredEmail: updatedPreferredEmail,
    country: creatorCountry,
    contentLanguages: [aLanguage({ value: "en" })],
    preferredFranchises: preferredFranchises,
    preferredLanguage: aCountry({ code: "en_US", name: "English" }),
    creatorTypes,
    contentUrls: [{ url: Random.url() }]
  });
  let creatorProgram = {
    code: "affiliate"
  };
  let expectedCompletedApplication = {
    defaultGamerTag: interestedCreator.defaultGamerTag,
    nucleusId: interestedCreator.nucleusId,
    firstName: interestedCreator.firstName,
    lastName: interestedCreator.lastName,
    originEmail: interestedCreator.originEmail,
    dateOfBirth: interestedCreator.dateOfBirth,
    preferredEmail: interestedCreator.preferredEmail,
    countryCode,
    creatorTypes: [
      (interestedCreator.creatorTypes[0] as unknown as { value: string }).value,
      (interestedCreator.creatorTypes[1] as unknown as { value: string })?.value
    ],
    contentLanguages: [interestedCreator.contentLanguages[0].value],
    additionalLinks: [interestedCreator.contentUrls[0].url],
    preferredFranchises: interestedCreator.preferredFranchises,
    preferredLanguage: interestedCreator.preferredLanguage,
    creatorProgram: creatorProgram
  };

  it("submits an interested creator information", async () => {
    const client = { post: jest.fn().mockReturnValue({ data: expectedCompletedApplication }) };
    const interestedCreatorsHttpClient = new InterestedCreatorsHttpClient(client as unknown as TraceableHttpClient);

    await interestedCreatorsHttpClient.addRequestToJoinProgram({
      ...interestedCreator,
      creatorProgram
    } as unknown as InterestedCreator);

    expect(client.post).toHaveBeenCalledTimes(1);
    expect(client.post).toHaveBeenCalledWith("/v4/creator-applications", { body: expectedCompletedApplication });
  });
});
