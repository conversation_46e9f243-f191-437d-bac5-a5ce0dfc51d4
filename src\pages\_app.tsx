import "@styles/globals.css";
import "@eait-playerexp-cn/core-ui-kit/dist/style/core-ui-kit.css";
import App, { AppContext, AppProps } from "next/app";
import React from "react";

const Application = ({ Component, pageProps }: AppProps): JSX.Element => {
  return <Component {...pageProps} />;
};

Application.getInitialProps = async (context: AppContext) => {
  return await App.getInitialProps(context);
};

export default Application;
