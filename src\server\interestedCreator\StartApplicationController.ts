import { Inject, Service } from "typedi";
import { NextApiResponse } from "next";
import { Controller, NextApiRequestWithSession, RequestHandler } from "@eait-playerexp-cn/server-kernel";
import config from "config";
@Service()
export default class StartApplicationController extends RequestHandler implements Controller {
  constructor(@Inject("supportedLocales") supportedLocales: string[]) {
    super({ supportedLocales });
  }
  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    await this.addToSession(req, "interestedCreator", true);
    this.redirectTo(res, `${config.REDIRECT_URL}/api/login`);
  }
}
