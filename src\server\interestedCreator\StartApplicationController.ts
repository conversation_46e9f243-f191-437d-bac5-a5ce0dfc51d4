import { Inject, Service } from "typedi";
import { NextApiResponse } from "next";
import { Controller, NextApiRequestWithSession, RequestHandler } from "@eait-playerexp-cn/server-kernel";
import config from "config";
import { runtimeConfig } from "@src/services/RuntimeConfig";
@Service()
export default class StartApplicationController extends RequestHandler implements Controller {
  constructor(@Inject("supportedLocales") supportedLocales: string[]) {
    super({ supportedLocales });
  }
  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    await this.addToSession(req, "interestedCreator", true);

    // Use runtime config with fallback to static config, then environment variables
    const runtimeRedirectUrl = runtimeConfig.getRedirectUrl();
    const redirectUrl = runtimeRedirectUrl || config.REDIRECT_URL || runtimeConfig.getRedirectUrlWithFallback();
    this.redirectTo(res, `${redirectUrl}/api/login`);
  }
}
