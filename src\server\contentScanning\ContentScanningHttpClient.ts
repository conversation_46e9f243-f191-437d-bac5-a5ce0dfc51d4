import { Inject, Service } from "typedi";
import type { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

export type ContentUrls = {
  urls: Array<string>;
};

type ScannedUrl = {
  url: string;
  isSecure: boolean;
};

export type ContentScanResult = {
  results: Array<ScannedUrl>;
};

export type ScanType = "INTERESTED_CREATORS" | "CREATORS";

@Service()
class ContentScanningHttpClient {
  constructor(@Inject("contentScanningClient") private readonly client: TraceableHttpClient) {}

  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/content-scanning/content-scanning-api/docs/api.html#operation/validateContentUrl Validate Content URL}
   */
  async verifyUrls(urls: ContentUrls, type: ScanType): Promise<ContentScanResult> {
    const scanResult = await this.client.post(`/v1/secure-content?type=${type}`, {
      body: { urls: urls }
    });
    return Promise.resolve(scanResult.data);
  }
}

export default ContentScanningHttpClient;
