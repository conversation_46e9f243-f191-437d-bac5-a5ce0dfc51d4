import { NextApiResponse } from "next";
import { Service } from "typedi";
import { Controller, NextApiRequestWithSession, RequestHandler } from "@eait-playerexp-cn/server-kernel";
import { HttpStatus } from "@eait-playerexp-cn/http";

@Service()
class ClearFacebookPagesController extends RequestHandler implements Controller {
  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    await this.removeFromSession(req, "fbPages");
    this.empty(res, HttpStatus.OK);
  }
}

export default ClearFacebookPagesController;
