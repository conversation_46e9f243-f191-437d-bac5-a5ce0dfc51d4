import { Inject, Service } from "typedi";
import { NextApiResponse } from "next";
import ContentScanning, { ContentUrls, ScanType } from "./ContentScanningHttpClient";
import { Controller, NextApiRequestWithSession, RequestHandler } from "@eait-playerexp-cn/server-kernel";

@Service()
class VerifyContentUrlController extends RequestHandler implements Controller {
  constructor(
    private readonly contentScanning: ContentScanning,
    @Inject("supportedLocales") supportedLocales: string[]
  ) {
    super({ supportedLocales });
  }
  async handle(req: NextApiRequestWithSession, res: NextApiResponse): Promise<void> {
    const urls = req?.body as unknown as ContentUrls;
    const type = req.query?.type as ScanType;
    const scanResult = await this.contentScanning.verifyUrls(urls, type);

    this.json(res, scanResult);
  }
}

export default VerifyContentUrlController;
