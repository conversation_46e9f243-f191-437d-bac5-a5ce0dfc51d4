import { Activity, ActivityRecorder } from "@eait-playerexp-cn/activity-feed";
import { context, isSpanContextValid, trace } from "@opentelemetry/api";

export default class TelemetryRecorder implements ActivityRecorder {
  record(activity: Activity): void {
    const span = trace.getSpan(context.active());
    const spanContext = span.spanContext();

    if (!isSpanContextValid(spanContext)) return;

    const tracingFields = {
      traceId: spanContext.traceId,
      correlationId: spanContext.traceId,
      spanId: spanContext.spanId
    };
    activity.context = { ...activity.context, ...tracingFields };
  }
}
