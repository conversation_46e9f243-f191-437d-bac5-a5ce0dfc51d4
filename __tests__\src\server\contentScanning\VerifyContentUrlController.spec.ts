import "reflect-metadata";
import { createMocks } from "node-mocks-http";
import { NextApiResponse } from "next";
import VerifyContentUrlController from "@src/server/contentScanning/VerifyContentUrlController";
import ContentScanningHttpClient from "@src/server/contentScanning/ContentScanningHttpClient";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";

describe("VerifyContentUrlController", () => {
  let controller: VerifyContentUrlController;

  beforeEach(() => jest.clearAllMocks());

  it("verify content urls successfully", async () => {
    const urls = ["https://reddit.com", "https://google.com"];
    const body = { urls };
    const { req, res } = createMocks({
      method: "POST",
      url: `/v1/secure-content/`,
      body
    });
    const verifyContentUrlResponse = [
      { url: "https://reddit.com", isSecure: true },
      { url: "https://google.com", isSecure: true }
    ];
    const mockVerifyContentUrl = jest.fn(() => Promise.resolve(verifyContentUrlResponse));
    controller = new VerifyContentUrlController(
      {
        verifyUrls: mockVerifyContentUrl
      } as unknown as ContentScanningHttpClient,
      []
    );

    await controller.handle(req as unknown as NextApiRequestWithSession, res as unknown as NextApiResponse);

    expect(res._getStatusCode()).toBe(200);
    expect(mockVerifyContentUrl).toHaveBeenCalledTimes(1);
  });

  it("verify content urls successfully with type 'CREATORS'", async () => {
    const urls = ["https://reddit.com"];
    const type = "CREATORS";
    const body = { urls };
    const { req, res } = createMocks({
      method: "POST",
      url: `/api/verify-content-url`,
      body,
      query: { type }
    });
    const verifyContentUrlResponse = [{ url: "https://reddit.com", isSecure: true }];
    const mockVerifyContentUrl = jest.fn(() => Promise.resolve(verifyContentUrlResponse));
    controller = new VerifyContentUrlController(
      {
        verifyUrls: mockVerifyContentUrl
      } as unknown as ContentScanningHttpClient,
      []
    );

    await controller.handle(req as unknown as NextApiRequestWithSession, res as unknown as NextApiResponse);

    expect(res._getStatusCode()).toBe(200);
    expect(mockVerifyContentUrl).toHaveBeenCalledTimes(1);
    expect(mockVerifyContentUrl).toHaveBeenCalledWith({ urls: ["https://reddit.com"] }, type);
  });
});
