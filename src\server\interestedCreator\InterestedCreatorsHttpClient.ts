import { Inject, Service } from "typedi";
import type { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import InterestedCreator from "./InterestedCreator";

@Service()
class InterestedCreatorsHttpClient {
  constructor(@Inject("operationsClient") private readonly client: TraceableHttpClient) {}
  /**
   * @see {@link https://eait-playerexp-cn.gitlab.ea.com/cn-services/operations/operations-api/docs/api.html#tag/Interested-Creators/operation/submitInterestedCreatorApplicationV4}
   */
  async addRequestToJoinProgram(interestedCreator: InterestedCreator): Promise<void> {
    const completedApplication = new InterestedCreator(interestedCreator).completeWithAdditionalLinks();
    await this.client.post(`/v4/creator-applications`, { body: completedApplication });
  }
}

export default InterestedCreatorsHttpClient;
