---
currentMenu: installation
---

# Installation

## Prerequisites

Please make sure the following software is installed in your local development environment before continuing.

- [Node and npm](https://nodejs.org/en/download/package-manager/)
- [npx](https://www.npmjs.com/package/npx#install)
- [Make](https://www.gnu.org/software/make/manual/make.html)
- [Docker](https://docs.docker.com/get-docker/)
- [Docker Compose](https://docs.docker.com/compose/install/)
- [AWS CLI](https://docs.aws.amazon.com/cli/latest/userguide/install-cliv1.html)
- [Amazon ECR Docker Credential Helper](https://github.com/awslabs/amazon-ecr-credential-helper#installing)

## Getting started

Please don't skip any of the sections below since all are required to be able to run this application locally.
All the sections below assume you have installed the software from the pre-requisites section above.

### Configure access to GitLab's npm registry

We have extracted a [component library](https://eait-playerexp-cn.gitlab.ea.com/creator-network-frontend/core-ui-kit/) for this application, and it's been published as a npm package in GitLab Registry.

In order to access the GitLab npm Registry, you'll need a [personal access token](https://gitlab.ea.com/-/profile/personal_access_tokens) with the `read_registry` and `api` grant.

Creat and add your access token for the scoped packages URL.
This will allow you to download `@eait-playerexp-cn/` packages from private projects.

```bash
npm config set -- '//gitlab.ea.com/api/v4/packages/npm/:_authToken' "<your_token>"
```

### Install npm dependencies and create `.env` file

Run the command below to install all the packages required to run this [Next.js](https://nextjs.org/docs) application.

```bash
make bootstrap
```

The above command will also create a `.env` file, required to preview the documentation via Docker container and run the API endpoints locally.

### Configuring AWS credentials

You should have installed the AWS CLI by now.
Please request the shared AWS configuration keys and execute the following command

```shell
aws configure
```

Enter the shared credentials, use `json` as default output, and `us-east-1` as default region.

You will need access to AWS in order to be able to run locally a Docker container whose image is in AWS ECR.

### Configuring AWS ECR Credential Helper

After installing the Amazon ECR Docker Credential Helper we need to configure it to access the registry where the Java API image is.

Ensure that your `~/.docker/configure.json` file includes the following key

```json
{
  "credHelpers": {
    "867742044088.dkr.ecr.us-east-1.amazonaws.com": "ecr-login"
  }
}
```

### Configuring Java API

The operations API needs to run locally in order to complete OAuth flows.
Step one is to create your database and run the migrations for the Operations API.

```bash
# Please wait a few seconds for the container to start
docker-compose up -d db
# Once the database is up run the migrations
make migrate
```

You won't need to change anything in your `.env` file except if you want MySQL to run in a different port (it runs on port `3306` by default).
In case you want MySQL running in a different port please update the `MYSQL_PORT` variable.

Once your database is up and running, you'll need to update a couple of variables from the `.env` file: `AWS_ACCESS_KEY_ID` and `AWS_SECRET_ACCESS_KEY` which you can get from `~/.aws/credentials`.
Copy the AWS secret and key to your `.env` file as well

```dotenv
AWS_ACCESS_KEY_ID=**************
AWS_SECRET_ACCESS_KEY=**************
```

That's it, you are all set!!

## Running the application

To run the development server execute the following command

```bash
make run
```

The command above will run the mock APIs in the background so that it can be consumed by the Next.js API endpoints.

## Previewing the components documentation

To preview the components in [Storybook](https://storybook.js.org/docs/react/get-started/introduction) run the following command

```bash
npm run storybook
```
