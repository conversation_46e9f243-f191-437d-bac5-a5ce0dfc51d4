services:
  docs:
    container_name: cn_applications_mfe_docs
    image: registry.gitlab.ea.com/dev-standardization/ci-cd-templates/docs:${TAG}
    ports:
      - "8090:8080"
    volumes:
      - ./:/usr/src/myapp
      - ~/.ssh:/root/.ssh
  redis:
    container_name: cn_applications_mfe_redis
    image: ${ARTIFACTORY}/redis/redis-stack:6.2.6-v17
    ports:
      - "6379:6379"
    volumes:
      - ./containers/redis:/data
