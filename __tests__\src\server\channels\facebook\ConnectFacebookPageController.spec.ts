import "reflect-metadata";
import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import ConnectFacebookPageController from "@src/server/channels/facebook/ConnectFacebookPageController";
import ConnectedAccountsHttpClient from "@src/server/connecteAccounts/ConnectedAccountsHttpClient";
import { Random } from "@eait-playerexp-cn/interested-creators-ui";
import config from "config";

describe("ConnectFacebookPageController", () => {
  let controller: ConnectFacebookPageController;
  let connectedAccounts: ConnectedAccountsHttpClient;
  const session = { save: jest.fn() };

  beforeEach(() => {
    jest.clearAllMocks();
    connectedAccounts = { connectFacebookPage: jest.fn() } as unknown as ConnectedAccountsHttpClient;
    controller = new ConnectFacebookPageController(connectedAccounts);
    config.FLAG_PER_PROGRAM_PROFILE = false;
  });

  it("connects a Facebook page for an interested creator", async () => {
    const credentials = {
      nucleusId: Random.nucleusId(),
      pageAccessToken: Random.string(20),
      pageId: Random.uuid(),
      creatorId: null
    };
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "POST",
      url: "/api/facebook-channels",
      body: credentials,
      session: { ...session, nucleusId: credentials.nucleusId }
    });

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(req.session.fbPages).toBeUndefined();
    expect(req.session.accountType).toEqual("Facebook");
    expect(req.session.save).toHaveBeenCalledTimes(2);
    expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledTimes(1);
    expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledWith(credentials);
  });

  it("connects a Facebook page for a creator", async () => {
    const credentials = {
      creatorId: Random.nucleusId(),
      pageAccessToken: Random.string(20),
      pageId: Random.uuid(),
      nucleusId: undefined
    };
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "POST",
      url: "/api/facebook-channels",
      body: credentials,
      session: { ...session, user: { id: credentials.creatorId } }
    });

    await controller.handle(req as unknown as NextApiRequestWithSession, res as unknown as NextApiResponse);

    expect(res._getStatusCode()).toBe(200);
    expect(req.session.fbPages).toBeUndefined();
    expect(req.session.accountType).toEqual("Facebook");
    expect(req.session.save).toHaveBeenCalledTimes(2);
    expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledTimes(1);
    expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledWith(credentials);
  });

  it("connects a Facebook page for an interested creator with number nucleusId", async () => {
    const credentials = {
      nucleusId: Random.nucleusId(),
      pageAccessToken: Random.string(),
      pageId: Random.uuid(),
      creatorId: null
    };
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "POST",
      url: "/api/facebook-channels",
      body: credentials,
      session: { ...session, nucleusId: credentials.nucleusId }
    });

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(req.session.fbPages).toBeUndefined();
    expect(req.session.accountType).toEqual("Facebook");
    expect(req.session.save).toHaveBeenCalledTimes(2);
    expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledTimes(1);
    expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledWith(credentials);
  });

  describe("with FLAG_PER_PROGRAM_PROFILE enabled", () => {
    beforeEach(() => {
      config.FLAG_PER_PROGRAM_PROFILE = true;
    });

    it("connects a Facebook page for an interested creator", async () => {
      config.FLAG_PER_PROGRAM_PROFILE = true;
      const nucleusId = Random.nucleusId();
      const credentials = {
        nucleusId,
        pageAccessToken: Random.string(),
        pageId: Random.uuid(),
        creatorId: null
      };
      const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
        method: "POST",
        url: "/api/facebook-channels",
        body: { pageId: credentials.pageId, pageAccessToken: credentials.pageAccessToken },
        session: {
          ...session,
          identity: { type: "INTERESTED_CREATOR", nucleusId }
        }
      });

      await controller.handle(req, res);

      expect(res._getStatusCode()).toBe(200);
      expect(req.session.fbPages).toBeUndefined();
      expect(req.session.accountType).toEqual("Facebook");
      expect(req.session.save).toHaveBeenCalledTimes(2);
      expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledTimes(1);
      expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledWith(credentials);
    });

    it("connects a Facebook page for a creator", async () => {
      config.FLAG_PER_PROGRAM_PROFILE = true;
      const creatorId = Random.nucleusId();
      const controller = new ConnectFacebookPageController(connectedAccounts);
      const credentials = {
        creatorId,
        pageAccessToken: Random.string(),
        pageId: Random.uuid(),
        nucleusId: undefined
      };
      const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
        method: "POST",
        url: "/api/facebook-channels",
        body: { pageId: credentials.pageId, pageAccessToken: credentials.pageAccessToken },
        session: { ...session, identity: { type: "CREATOR", id: creatorId } }
      });

      await controller.handle(req, res);

      expect(res._getStatusCode()).toBe(200);
      expect(req.session.fbPages).toBeUndefined();
      expect(req.session.accountType).toEqual("Facebook");
      expect(req.session.save).toHaveBeenCalledTimes(2);
      expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledTimes(1);
      expect(connectedAccounts.connectFacebookPage).toHaveBeenCalledWith(credentials);
    });
  });
});
