import { createMocks, MockResponse } from "node-mocks-http";
import { NextApiResponse } from "next";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import UpdateApplicationController from "@src/server/interestedCreator/UpdateApplicationController";
import { aLocalizedDate } from "__tests__/factories/LocalizedDateBuilder";
import { aCountry, aLanguage } from "@eait-playerexp-cn/metadata-test-fixtures";
import { Random } from "@eait-playerexp-cn/interested-creators-ui";
import { aStoredIdentity } from "@eait-playerexp-cn/identity-test-fixtures";
import { Identity } from "@eait-playerexp-cn/identity-types";

describe("UpdateApplicationController", () => {
  let controller: UpdateApplicationController;
  const session = { save: jest.fn() };
  const programCode = "affiliate";
  const interestedCreator = {
    type: "INTERESTED_CREATOR",
    id: Random.uuid(),
    email: Random.email(),
    username: Random.lastName(),
    name: Random.firstName()
  };
  const identity = {
    ...Identity.fromStored(aStoredIdentity(interestedCreator)),
    programs: [{ code: programCode }]
  };

  beforeEach(() => {
    jest.clearAllMocks();
    controller = new UpdateApplicationController();
  });

  it("updates interested creator in session", async () => {
    const creator = {
      nucleusId: Random.nucleusId(),
      defaultGamerTag: Random.string(),
      originEmail: "<EMAIL>",
      dateOfBirth: aLocalizedDate().minusYears(18).build().formatWithEpoch("DD/MM/YYYY")
    };
    const updatedCreator = {
      ...creator,
      contentAccounts: [],
      creatorTypes: [],
      preferredFranchises: [],
      firstName: "Jane",
      lastName: "Doe",
      country: aCountry({ label: "Canada" }),
      contentUrls: [{ url: "https://youtube.com", followers: 3456789 }],
      contentLanguages: [aLanguage({ value: "en", label: "English" })]
    };
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "PUT",
      url: "/api/applications",
      body: updatedCreator,
      session: {
        ...session,
        [`${programCode}.interestedCreator`]: true,
        identity
      }
    });

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(updatedCreator);
    expect(req.session[`${programCode}.interestedCreator`]).toEqual(updatedCreator);
    expect(req.session.save).toHaveBeenCalled();
  });

  it("handles missing creator in session", async () => {
    const updatedCreator = {
      firstName: "Jane",
      lastName: "Doe",
      contentLanguages: [],
      contentUrls: [],
      creatorTypes: [],
      preferredFranchises: []
    };
    const { req, res }: { req: NextApiRequestWithSession; res: MockResponse<NextApiResponse> } = createMocks({
      method: "PUT",
      url: "/api/applications",
      body: updatedCreator,
      session: { ...session, identity }
    });

    await controller.handle(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(updatedCreator);
    expect(req.session[`${programCode}.interestedCreator`]).toEqual(updatedCreator);
    expect(req.session.save).toHaveBeenCalled();
  });
});
