import "reflect-metadata";
import { NextApiResponse } from "next";
import { createRouter } from "next-connect";
import onError from "@src/middleware/OAuthErrorHandler";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  NextApiRequestWithSession
} from "@eait-playerexp-cn/server-kernel";
import ApiContainer from "@src/ApiContainer";
import ClearAccountTypeController from "@src/server/connecteAccounts/ClearAccountTypeController";
import session from "@src/middleware/Session";
import withCors from "@src/middleware/WithCors";
import corsPreflight from "@src/middleware/CorsPreflifight";
import { addTelemetryInformation } from "@eait-playerexp-cn/identity";
import config from "../../../config";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .delete(
    withCors(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
      const controller = ApiContainer.get(ClearAccountTypeController);
      await controller.handle(req, res);
    })
  )
  .all(corsPreflight);

export default router.handler({ onError });
