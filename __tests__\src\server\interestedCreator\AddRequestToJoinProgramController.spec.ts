import "reflect-metadata";
import { createMocks } from "node-mocks-http";
import { NextApiResponse } from "next";
import InterestedCreatorsHttpClient from "@src/server/interestedCreator/InterestedCreatorsHttpClient";
import { anInterestedCreator } from "@eait-playerexp-cn/interested-creators-ui";
import { NextApiRequestWithSession } from "@eait-playerexp-cn/server-kernel";
import AddRequestToJoinProgramController from "@src/server/interestedCreator/AddRequestToJoinProgramController";
import config from "../../../../config";
import { runtimeConfig } from "@src/services/RuntimeConfig";

jest.mock("../../../../config");

describe("AddRequestToJoinProgramController", () => {
  beforeEach(() => {
    config.PROGRAM_CODE = "affiliate";
    // Reset runtime config before each test
    runtimeConfig.reset();
  });

  it("saves an interested creator information using config fallback", async () => {
    const interestedCreator = anInterestedCreator();
    const { req, res } = createMocks({
      method: "POST",
      url: "/api/v4/interested-creators",
      body: interestedCreator
    });
    const interestedCreators = { addRequestToJoinProgram: jest.fn() };
    const controller = new AddRequestToJoinProgramController(
      interestedCreators as unknown as InterestedCreatorsHttpClient,
      []
    );

    await controller.handle(req as unknown as NextApiRequestWithSession, res as unknown as NextApiResponse);

    expect(res._getStatusCode()).toBe(201);
    expect(interestedCreators.addRequestToJoinProgram).toHaveBeenCalledTimes(1);
    expect(interestedCreators.addRequestToJoinProgram).toHaveBeenCalledWith({
      ...interestedCreator,
      creatorProgram: {
        code: "affiliate"
      }
    });
  });

  it("saves an interested creator information using runtime config", async () => {
    // Initialize runtime config
    runtimeConfig.initialize({
      programCode: "runtime-program"
    });

    const interestedCreator = anInterestedCreator();
    const { req, res } = createMocks({
      method: "POST",
      url: "/api/v4/interested-creators",
      body: interestedCreator
    });
    const interestedCreators = { addRequestToJoinProgram: jest.fn() };
    const controller = new AddRequestToJoinProgramController(
      interestedCreators as unknown as InterestedCreatorsHttpClient,
      []
    );

    await controller.handle(req as unknown as NextApiRequestWithSession, res as unknown as NextApiResponse);

    expect(res._getStatusCode()).toBe(201);
    expect(interestedCreators.addRequestToJoinProgram).toHaveBeenCalledTimes(1);
    expect(interestedCreators.addRequestToJoinProgram).toHaveBeenCalledWith({
      ...interestedCreator,
      creatorProgram: {
        code: "runtime-program"
      }
    });
  });

  it("prefers runtime config over static config", async () => {
    // Initialize runtime config with different program code
    runtimeConfig.initialize({
      programCode: "priority-program"
    });

    const interestedCreator = anInterestedCreator();
    const { req, res } = createMocks({
      method: "POST",
      url: "/api/v4/interested-creators",
      body: interestedCreator
    });
    const interestedCreators = { addRequestToJoinProgram: jest.fn() };
    const controller = new AddRequestToJoinProgramController(
      interestedCreators as unknown as InterestedCreatorsHttpClient,
      []
    );

    await controller.handle(req as unknown as NextApiRequestWithSession, res as unknown as NextApiResponse);

    expect(res._getStatusCode()).toBe(201);
    expect(interestedCreators.addRequestToJoinProgram).toHaveBeenCalledTimes(1);
    expect(interestedCreators.addRequestToJoinProgram).toHaveBeenCalledWith({
      ...interestedCreator,
      creatorProgram: {
        code: "priority-program" // Should use runtime config, not "affiliate"
      }
    });
  });
});
