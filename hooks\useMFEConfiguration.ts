/**
 * Custom hook for managing MFE configuration in host applications
 */

import { useEffect, useState, useCallback } from 'react';

// These will be dynamically imported from the MFE
let initializeMFE: any = null;
let isMFEReady: any = null;
let getMFEStatus: any = null;
let resetMFE: any = null;

interface MFEConfig {
  redirectUrl: string;
  programCode: string;
}

interface UseMFEConfigurationOptions extends MFEConfig {
  /**
   * Whether to validate configuration after initialization
   * @default true
   */
  validate?: boolean;
  
  /**
   * Whether to throw an error if validation fails
   * @default false
   */
  throwOnValidationError?: boolean;
  
  /**
   * Custom validation error callback
   */
  onValidationError?: (missingFields: string[]) => void;

  /**
   * Whether to automatically retry on failure
   * @default false
   */
  autoRetry?: boolean;

  /**
   * Number of retry attempts
   * @default 3
   */
  maxRetries?: number;
}

interface UseMFEConfigurationResult {
  isReady: boolean;
  isLoading: boolean;
  error: string | null;
  retry: () => void;
  status: any;
}

/**
 * Hook to manage MFE configuration with dynamic imports
 */
export const useMFEConfiguration = (
  options: UseMFEConfigurationOptions
): UseMFEConfigurationResult => {
  const [isReady, setIsReady] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [status, setStatus] = useState<any>(null);
  const [retryCount, setRetryCount] = useState(0);

  const {
    redirectUrl,
    programCode,
    validate = true,
    throwOnValidationError = false,
    onValidationError,
    autoRetry = false,
    maxRetries = 3
  } = options;

  // Load MFE initializer functions dynamically
  const loadMFEServices = useCallback(async () => {
    try {
      if (!initializeMFE) {
        const mfeInitializer = await import('applications/MFEInitializer');
        initializeMFE = mfeInitializer.initializeMFE;
        isMFEReady = mfeInitializer.isMFEReady;
        getMFEStatus = mfeInitializer.getMFEStatus;
        resetMFE = mfeInitializer.resetMFE;
      }
      return true;
    } catch (err) {
      console.error('Failed to load MFE services:', err);
      return false;
    }
  }, []);

  // Initialize MFE configuration
  const initialize = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load MFE services first
      const servicesLoaded = await loadMFEServices();
      if (!servicesLoaded) {
        throw new Error('Failed to load MFE services');
      }

      // Initialize MFE with configuration
      const result = initializeMFE({
        redirectUrl,
        programCode,
        validate,
        throwOnValidationError,
        onValidationError
      });

      if (result.success && isMFEReady()) {
        setIsReady(true);
        setStatus(getMFEStatus());
        setRetryCount(0);
      } else {
        const errorMessage = result.error || 'MFE configuration failed';
        throw new Error(errorMessage);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown configuration error';
      setError(errorMessage);
      setIsReady(false);
      
      // Auto retry if enabled and within retry limit
      if (autoRetry && retryCount < maxRetries) {
        setTimeout(() => {
          setRetryCount(prev => prev + 1);
        }, 1000 * (retryCount + 1)); // Exponential backoff
      }
    } finally {
      setIsLoading(false);
    }
  }, [
    redirectUrl,
    programCode,
    validate,
    throwOnValidationError,
    onValidationError,
    autoRetry,
    maxRetries,
    retryCount,
    loadMFEServices
  ]);

  // Manual retry function
  const retry = useCallback(() => {
    if (resetMFE) {
      resetMFE();
    }
    setIsReady(false);
    setError(null);
    setRetryCount(0);
    initialize();
  }, [initialize]);

  // Initialize on mount and when dependencies change
  useEffect(() => {
    initialize();
  }, [initialize]);

  // Auto retry effect
  useEffect(() => {
    if (autoRetry && retryCount > 0 && retryCount <= maxRetries && error) {
      initialize();
    }
  }, [retryCount, autoRetry, maxRetries, error, initialize]);

  return {
    isReady,
    isLoading,
    error,
    retry,
    status
  };
};

/**
 * Simple hook for basic MFE configuration
 */
export const useSimpleMFEConfiguration = (config: MFEConfig) => {
  return useMFEConfiguration({
    ...config,
    validate: true,
    throwOnValidationError: false,
    autoRetry: true,
    maxRetries: 3
  });
};

export default useMFEConfiguration;
