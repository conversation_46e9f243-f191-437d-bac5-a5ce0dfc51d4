import { LogLevel } from "@eait-playerexp-cn/activity-logger";
import { RedisClientOptions, ScaleReadType, SessionOptions } from "@eait-playerexp-cn/server-kernel";

const redisClient = new RedisClientOptions(
  process.env.REDIS_HOST,
  +process.env.REDIS_PORT,
  process.env.REDIS_SCALE_READ as ScaleReadType
);

const config = {
  APP_ENV: process.env.APP_ENV,
  SENTRY_DSN: process.env.SENTRY_DSN,
  SUPPORTED_LOCALES: JSON.parse(process.env.SUPPORTED_LOCALES),
  HTTP_REQUEST_TIMEOUT: +process.env.HTTP_REQUEST_TIMEOUT,
  LOG_LEVEL: process.env.LOG_LEVEL as LogLevel,
  API_CLIENT_ID: process.env.API_CLIENT_ID,
  API_CLIENT_SECRET: process.env.API_CLIENT_SECRET,
  METADATA_API_BASE_URL: process.env.METADATA_API_BASE_URL,
  CONTENT_SCANNING_API_BASE_URL: process.env.CONTENT_SCANNING_API_BASE_URL,
  CONTENT_SUBMISSION_BASE_URL: process.env.CONTENT_SUBMISSION_BASE_URL,
  DEFAULT_FRANCHISE_IMAGE: process.env.DEFAULT_FRANCHISE_IMAGE,
  ACCESS_TOKEN_BASE_URL: process.env.ACCESS_TOKEN_BASE_URL,
  REDIRECT_URL: process.env.REDIRECT_URL,
  OPERATIONS_API_BASE_URL: process.env.OPERATIONS_API_BASE_URL,
  APP_DEBUG: process.env.APP_DEBUG === "true",
  PROGRAM_CODE: process.env.PROGRAM_CODE,
  INTERESTED_CREATOR_REAPPLY_PERIOD: process.env.INTERESTED_CREATOR_REAPPLY_PERIOD === "true",
  redisClient,
  sessionOptions: {
    cookies: {
      secret: process.env.COOKIE_PASSWORD,
      httpOnly: process.env.COOKIE_HTTP_ONLY === "true",
      sameSite: process.env.COOKIE_SAME_SITE,
      domain: !!process.env.COOKIE_DOMAIN ? process.env.COOKIE_DOMAIN : undefined,
      secure: process.env.COOKIE_SECURE === "true"
    },
    ttl: +process.env.SESSION_TTL,
    proxy: process.env.SESSION_PROXY === "true"
  } as SessionOptions,
  cacheOptions: {
    cachePrefix: process.env.CACHE_PREFIX,
    redisClient
  },
  corsConfiguration: {
    allowCredentials: process.env.CORS_ALLOW_CREDENTIALS,
    allowedMethods: process.env.CORS_ALLOWED_METHODS,
    allowedOrigins: process.env.CORS_ALLOWED_ORIGINS?.split(","),
    allowedHeaders: process.env.CORS_ALLOWED_HEADERS
  },
  FLAG_PER_PROGRAM_PROFILE: process.env.FLAG_PER_PROGRAM_PROFILE === "true"
};

export default config;
