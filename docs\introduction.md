---
currentMenu: introduction
---

# Code organization

[Next.js](https://nextjs.org/docs) applications have three main different types of elements

- React Components
- Pages
- API Routes

## React components

Components should be created under the `components` directory.

- Components follow camel case naming convention for both the component, and the file name
- CSS for components should use the same filename and be saved in the same directory next to its component
- Components should be grouped per flow or per page into directories following [kebab case](https://en.wiktionary.org/wiki/kebab_case) naming convention. See this file/directory structure for the components for the [Interested Creators Flow](https://gitlab.ea.com/eait-playerexp-cn/creator-network-frontend/creator-network-website/-/tree/main/components/pages/interested-creators)

## Pages

In order for pages to be automatically detected by the Next.js router they need to be saved in the `pages` directory.

Since Next.js uses the file name for the route path, we follow a slightly different convention for pages.

- Component names should be camel case
- File names should follow kebab case

## API routes

The `pages/api` directory is mapped to `/api/*`. Files in this directory are treated as [API routes](https://nextjs.org/docs/api-routes/introduction) instead of React pages.

## More resources

Please review our [how-to guidelines](https://eait-playerexp-cn.gitlab.ea.com/creator-network-frontend/creator-network-website/docs/how-to.html) for more details on how to structure components and pages
