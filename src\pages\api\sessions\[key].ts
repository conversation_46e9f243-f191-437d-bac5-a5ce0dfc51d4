import "reflect-metadata";
import { NextApiResponse } from "next";
import ApiContainer from "@src/ApiContainer";
import { createRouter } from "next-connect";
import onError from "@src/middleware/OAuthErrorHandler";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  NextApiRequestWithSession
} from "@eait-playerexp-cn/server-kernel";
import ClearSessionsKeyController from "@src/server/connecteAccounts/ClearSessionsKeyController";
import session from "@src/middleware/Session";
import corsPreflight from "@src/middleware/CorsPreflifight";
import withCors from "@src/middleware/WithCors";
import { addTelemetryInformation } from "@eait-playerexp-cn/identity";
import config from "../../../../config";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .delete(
    withCors(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
      const controller = ApiContainer.get(ClearSessionsKeyController);
      await controller.handle(req, res);
    })
  )
  .all(corsPreflight);

export default router.handler({ onError });
