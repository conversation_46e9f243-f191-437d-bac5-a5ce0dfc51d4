// This file sets a custom webpack configuration to use your Next.js app
// with Sentry.
// https://nextjs.org/docs/api-reference/next.config.js/introduction
// https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/
const { withSentryConfig } = require("@sentry/nextjs");
const { i18n } = require("./next-i18next.config");
const NextFederationPlugin = require("@module-federation/nextjs-mf");

module.exports = {
  output: "standalone",
  webpack(config) {
    config.output.environment = {
      ...config.output.environment,
      dynamicImport: true
    };
    const { isServer, plugins } = config;
    const location = isServer ? "ssr" : "chunks";
    plugins.push(
      new NextFederationPlugin({
        name: "creator-hub",
        filename: "creator-hub.js",
        remotes: {
          notifications: `notifications@${process.env.NOTIFICATIONS_MFE_BASE_URL}/_next/static/${location}/notifications-mfe.js`,
          applications: `applications@${process.env.APPLICATIONS_MFE_BASE_URL}/_next/static/${location}/applications-mfe.js`
        }
      })
    );

    return config;
  },
  i18n,
  reactStrictMode: false,
  sentry: {
    hideSourceMaps: true
  },
  publicRuntimeConfig: {
    GTM_AUTH: process.env.GTM_AUTH,
    GTM_PREVIEW: process.env.GTM_PREVIEW,
    AMPLITUDE_API_KEY: process.env.AMPLITUDE_API_KEY,
    AMPLITUDE_ENV: process.env.AMPLITUDE_ENV,
    BUILD_VERSION: process.env.RELEASE_VERSION,
    SENTRY_DSN: process.env.SENTRY_DSN,
    APP_ENV: process.env.APP_ENV,
    APP_DEBUG: process.env.APP_DEBUG,
    INITIAL_MESSAGE_TITLE: process.env.INITIAL_MESSAGE_TITLE,
    INITIAL_MESSAGE_DESCRIPTION: process.env.INITIAL_MESSAGE_DESCRIPTION,
    SUPPORTED_LOCALES: process.env.SUPPORTED_LOCALES,
    ANALYTICS_SAMPLE_RATE: process.env.ANALYTICS_SAMPLE_RATE,
    HTTP_REQUEST_TIMEOUT: +process.env.HTTP_REQUEST_TIMEOUT,
    FLAG_OBSERVABILITY: process.env.FLAG_OBSERVABILITY,
    METADATA_API_BASE_URL: process.env.METADATA_API_BASE_URL,
    SERVICE_NAME: process.env.SERVICE_NAME,
    NOTIFICATIONS_MFE_BASE_URL: process.env.NOTIFICATIONS_MFE_BASE_URL,
    NOTIFICATIONS_MFE_NAME: process.env.NOTIFICATIONS_MFE_NAME,
    FLAG_NEW_NAVIGATION_ENABLED: process.env.FLAG_NEW_NAVIGATION_ENABLED,
    
    // MFE Configuration - Add these new environment variables
    CREATOR_PROGRAM_REDIRECT_URL: process.env.CREATOR_PROGRAM_REDIRECT_URL,
    CREATOR_PROGRAM_CODE: process.env.CREATOR_PROGRAM_CODE,
    CREATORS_API_BASE_URL: process.env.CREATORS_API_BASE_URL,
    APPLICATIONS_MFE_BASE_URL: process.env.APPLICATIONS_MFE_BASE_URL
  },
  experimental: {
    instrumentationHook: true,
    serverComponentsExternalPackages: ["@opentelemetry/sdk-node", "next-connect"]
  }
};

module.exports = withSentryConfig(module.exports, { silent: true });
