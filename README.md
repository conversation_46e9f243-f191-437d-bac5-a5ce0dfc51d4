# 🎮 Module Federation Template

This GitLab project template can be used to start a federated service.

## ✅ Tasks to do before your first green pipeline

After creating a new micro frontend using this template your pipeline will fail unless you work on the following tasks.

1. You will need a new Docker repository (in pre-production and production) for your Micro frontend image.
   Repository names have the following naming convention is `eait.playerexp.cn/<name>-microfrontend` where `<name>` is the name of your application in kebab-case.
2. You will need a new AWS Secret per environment. Secret names have the following naming convention `/cn/secrets/cn-<name>-mfe_<env>` where `<name>` is the name of your application in kebab-case, and `<env>` is the name of the environment you will deploy `dev`, `qa`, `uat`, `regress` or `prod`.
3. You will need to create a new Terraform module for your micro frontend.
   You will need a new directory `modules/<name>-mfe` where `<name>` is the name of your application in kebab-case.
   Please copy the files in [this directory](https://gitlab.ea.com/eait-playerexp-cn/cloud-infra-as-code/-/tree/main/modules/notifications-mfe) and replace `notifications` with your micro frontend name in kebab-case.
   Please also add your new module to `main.tf`

```terraform
module "cn-<name>-mfe" {
  source  = "./modules/<name>-mfe"
  env     = var.env
}
```

4. You will also need a new Helm chart for your micro frontend.
   You will need a new directory `cn-services/charts/cn-<name>-mfe`.
   Please copy the files in [this directory](https://gitlab.ea.com/eait-playerexp-cn/cn-deployments/-/tree/main/cn-services/charts/cn-notifications-mfe) and replace `notifications` with your micro frontend name in kebab-case.
   You will also need a new job to generate the newly created chart.

```yaml
charts:<name>-mfe:
  <<: *test_charts
  script:
    - cd cn-<name>-mfe
    - helm lint .
  artifacts:
    name: $CI_JOB_NAME
    paths:
      - cn-services/charts/cn-<name>-mfe
```

## 💬 Documentation

For more details visit the [documentation](https://eait-playerexp-cn.gitlab.ea.com/creator-network-frontend/creator-network-website/) page

## 🙌 Contribute

Please review the [contribution guidelines](https://eait-playerexp-cn.gitlab.ea.com/creator-network-frontend/creator-network-website/contributing.html) for this project.

## ✨ Changes

All notable changes are documented in the [changelog](https://eait-playerexp-cn.gitlab.ea.com/creator-network-frontend/creator-network-website/changelog.html).
