import { AxiosError } from "axios";
import { Activity } from "@eait-playerexp-cn/activity-feed";
import { HttpRequest } from "@eait-playerexp-cn/http";

export default class ApplicationActivity {
  static applicationError(error: Error | AxiosError, request: HttpRequest): Activity {
    return Activity.error("application-error", error.message || "No message provided", {
      exception: error,
      request
    });
  }
}
