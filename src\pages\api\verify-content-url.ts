import "reflect-metadata";
import ApiContainer from "@src/ApiContainer";
import VerifyContentUrlController from "@src/server/contentScanning/VerifyContentUrlController";
import { NextApiResponse } from "next";
import { createRouter } from "next-connect";
import {
  addTelemetryInformation as legacyAddTelemetryInformation,
  NextApiRequestWithSession
} from "@eait-playerexp-cn/server-kernel";
import corsPreflight from "@src/middleware/CorsPreflifight";
import session from "@src/middleware/Session";
import onError from "@src/middleware/OnError";
import withCors from "@src/middleware/WithCors";
import { addTelemetryInformation } from "@eait-playerexp-cn/identity";
import config from "../../../config";

const router = createRouter<NextApiRequestWithSession, NextApiResponse>();

router
  .use(session)
  .use(config.FLAG_PER_PROGRAM_PROFILE ? addTelemetryInformation : legacyAddTelemetryInformation)
  .post(
    withCors(async (req: NextApiRequestWithSession, res: NextApiResponse) => {
      const controller = ApiContainer.get(VerifyContentUrlController);
      await controller.handle(req, res);
    })
  )
  .all(corsPreflight);

export default router.handler({ onError });
